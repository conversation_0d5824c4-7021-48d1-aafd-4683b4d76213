const mqtt = require('mqtt');
const readline = require('readline');

// Parse command line arguments
const args = process.argv.slice(2);
const brokerUrl = args[0] || 'ws://*************:8083/mqtt';
const topic = args[1] || 'aslaa/test';

// Extract protocol, host, and port from the broker URL
let protocol, host, port, path;
try {
  const url = new URL(brokerUrl);
  protocol = url.protocol.replace(':', '');
  host = url.hostname;
  port = url.port || (protocol === 'ws' ? 8083 : protocol === 'wss' ? 8084 : null);
  path = url.pathname || '/mqtt';
} catch (error) {
  console.error('Invalid broker URL format. Use ws://host:port/path or wss://host:port/path');
  process.exit(1);
}

// MQTT client options
const options = {
  clientId: `aslaacv_ws_cli_${Math.random().toString(16).substring(2, 10)}`,
  clean: true,
  connectTimeout: 4000,
  reconnectPeriod: 1000,
  keepalive: 60,
  protocolId: 'MQTT',
  protocolVersion: 4,
  rejectUnauthorized: false,
};

console.log('Connecting to MQTT broker via WebSocket:', brokerUrl);
console.log('Topic:', topic);
console.log('Options:', JSON.stringify(options, null, 2));

// Create MQTT client
const client = mqtt.connect(brokerUrl, options);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Set up event handlers
client.on('connect', () => {
  console.log('Connected to MQTT broker successfully via WebSocket!');

  // Subscribe to the topic
  client.subscribe(topic, (err) => {
    if (!err) {
      console.log(`Subscribed to ${topic}`);
      console.log('\nType a message and press Enter to publish to the topic.');
      console.log('Type "exit" to quit.\n');

      // Start reading user input
      promptUser();
    } else {
      console.error('Error subscribing:', err.message);
      client.end();
      rl.close();
      process.exit(1);
    }
  });
});

client.on('message', (receivedTopic, message) => {
  const msg = message.toString();
  console.log(`\nReceived message on ${receivedTopic}: ${msg}`);
  promptUser();
});

client.on('error', (err) => {
  console.error('MQTT Error:', err.message);
  console.error('Error details:', JSON.stringify(err, Object.getOwnPropertyNames(err)));
});

client.on('close', () => {
  console.log('Connection closed');
});

client.on('offline', () => {
  console.log('Client is offline');
});

client.on('reconnect', () => {
  console.log('Attempting to reconnect...');
});

// Function to prompt user for input
function promptUser() {
  rl.question('> ', (message) => {
    if (message.toLowerCase() === 'exit') {
      console.log('Disconnecting...');
      client.end();
      rl.close();
      process.exit(0);
    } else {
      // Publish the message
      client.publish(topic, message, { qos: 0 }, (error) => {
        if (error) {
          console.error('Error publishing:', error.message);
        }
      });
    }
  });
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Disconnecting...');
  client.end();
  rl.close();
  process.exit(0);
});
