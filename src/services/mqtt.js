import mqtt from 'mqtt';

// MQTT broker configuration
const MQTT_CONFIG = {
  // Client settings
  clientId: `aslaacv_${Math.random().toString(16).substring(2, 10)}`, // Random client ID
  clean: true,
  connectTimeout: 4000,
  reconnectPeriod: 1000,
  // WebSocket specific options
  keepalive: 60,
  protocolId: 'MQTT',
  protocolVersion: 4,
  rejectUnauthorized: false
};

// Primary MQTT broker URL (WebSocket)
const PRIMARY_BROKER = 'ws://39.104.209.84:8083/mqtt';

// Backup MQTT broker URLs (WebSocket)
const BACKUP_BROKERS = [
  'ws://39.104.209.84:8084/mqtt',
  'wss://broker.emqx.io:8084/mqtt' // Public EMQX broker as last resort
];

// Current broker URL
let currentBrokerIndex = 0;
let connectUrl = PRIMARY_BROKER;

// MQTT client instance
let client = null;

// Event callbacks
const callbacks = {
  connect: [],
  message: [],
  error: [],
  reconnect: [],
  offline: [],
  disconnect: [],
};

/**
 * Initialize MQTT connection
 * @returns {Object} MQTT client instance
 */
/**
 * Try to connect to the next broker in the list
 */
const tryNextBroker = () => {
  currentBrokerIndex = (currentBrokerIndex + 1) % (BACKUP_BROKERS.length + 1);
  connectUrl = currentBrokerIndex === 0 ? PRIMARY_BROKER : BACKUP_BROKERS[currentBrokerIndex - 1];
  console.log(`Switching to broker: ${connectUrl}`);
  return connectUrl;
};

/**
 * Initialize MQTT connection with fallback support
 * @returns {Object} MQTT client instance
 */
export const initMqtt = () => {
  if (client && client.connected) {
    return client;
  }

  // If client exists but is not connected, end it
  if (client) {
    client.end(true);
    client = null;
  }

  // console.log('Connecting to MQTT broker:', connectUrl);
  // console.log('With config:', JSON.stringify(MQTT_CONFIG));

  try {
    // Create MQTT client
    client = mqtt.connect(connectUrl, MQTT_CONFIG);
    // console.log('MQTT client created');

    // Set up reconnect logic with broker fallback
    let reconnectCount = 0;
    client.on('reconnect', () => {
      reconnectCount++;
      // console.log(`Reconnect attempt ${reconnectCount}`);

      // After 3 failed reconnect attempts, try the next broker
      if (reconnectCount >= 3) {
        reconnectCount = 0;
        connectUrl = tryNextBroker();
        client.options.href = connectUrl;
      }
    });
  } catch (error) {
    // console.error('Error creating MQTT client:', error);
    // console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));

    // Try next broker on connection error
    connectUrl = tryNextBroker();
  }

  // Set up event handlers
  client.on('connect', () => {
    // console.log('Connected to MQTT broker');
    callbacks.connect.forEach(callback => callback());
  });

  client.on('message', (topic, message) => {
    // console.log(`Received message on topic ${topic}: ${message.toString()}`);
    callbacks.message.forEach(callback => callback(topic, message));
  });

  client.on('error', (error) => {
    // console.error('MQTT connection error:', error);
    // console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    callbacks.error.forEach(callback => callback(error));
  });

  client.on('reconnect', () => {
    // console.log('Reconnecting to MQTT broker');
    callbacks.reconnect.forEach(callback => callback());
  });

  client.on('offline', () => {
    // console.log('MQTT client is offline');
    callbacks.offline.forEach(callback => callback());
  });

  client.on('disconnect', () => {
    // console.log('Disconnected from MQTT broker');
    callbacks.disconnect.forEach(callback => callback());
  });

  return client;
};

/**
 * Subscribe to a topic
 * @param {string} topic - Topic to subscribe to
 * @param {Object} options - Subscription options
 * @returns {boolean} Success status
 */
export const subscribe = (topic, options = { qos: 0 }) => {
  if (!client || !client.connected) {
    // console.error('MQTT client not connected');
    return false;
  }

  client.subscribe(topic, options, (error) => {
    if (error) {
      // console.error(`Error subscribing to ${topic}:`, error);
      return false;
    }
    // console.log(`Subscribed to ${topic}`);
  });

  return true;
};

/**
 * Publish a message to a topic
 * @param {string} topic - Topic to publish to
 * @param {string} message - Message to publish
 * @param {Object} options - Publish options
 * @returns {boolean} Success status
 */
export const publish = (topic, message, options = { qos: 0, retain: false }) => {
  if (!client || !client.connected) {
    // console.error('MQTT client not connected');
    return false;
  }

  client.publish(topic, message, options, (error) => {
    if (error) {
      // console.error(`Error publishing to ${topic}:`, error);
      return false;
    }
    // console.log(`Published to ${topic}: ${message}`);
  });

  return true;
};

/**
 * Unsubscribe from a topic
 * @param {string} topic - Topic to unsubscribe from
 * @returns {boolean} Success status
 */
export const unsubscribe = (topic) => {
  if (!client || !client.connected) {
    // console.error('MQTT client not connected');
    return false;
  }

  client.unsubscribe(topic, (error) => {
    if (error) {
      // console.error(`Error unsubscribing from ${topic}:`, error);
      return false;
    }
    // console.log(`Unsubscribed from ${topic}`);
  });

  return true;
};

/**
 * Add event listener
 * @param {string} event - Event name (connect, message, error, etc.)
 * @param {Function} callback - Callback function
 */
export const on = (event, callback) => {
  if (callbacks[event]) {
    callbacks[event].push(callback);
  }
};

/**
 * Remove event listener
 * @param {string} event - Event name
 * @param {Function} callback - Callback function to remove
 */
export const off = (event, callback) => {
  if (callbacks[event]) {
    callbacks[event] = callbacks[event].filter(cb => cb !== callback);
  }
};

/**
 * Disconnect MQTT client
 */
export const disconnect = () => {
  if (client && client.connected) {
    client.end();
    client = null;
    // console.log('MQTT client disconnected');
  }
};

const mqttService = {
  initMqtt,
  subscribe,
  publish,
  unsubscribe,
  on,
  off,
  disconnect,
};

export default mqttService;
