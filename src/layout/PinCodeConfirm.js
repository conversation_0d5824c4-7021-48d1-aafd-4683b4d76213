
import PropTypes from 'prop-types';
import {  useState } from 'react';
// @mui
import { Stack, Button, Dialog, Card, IconButton, Typography, TextField, Divider, Alert, } from '@mui/material';

// axios
import Iconify from '../components/Iconify';
import useAuth from '../hooks/useAuth';

// ----------------------------------------------------------------------


PinCodeConfirm.propTypes = {
  onSuccess: PropTypes.func,
  onModalClose: PropTypes.func,
  phoneNumber: PropTypes.string,

}
export default function PinCodeConfirm({ onModalClose, onSuccess, phoneNumber, ...other }) {
  const { user } = useAuth();
  const [mismatch, setMismatch] = useState(false);
  const [pincode, setPincode] = useState('');
  const handleOldPin = (event) => {
    setPincode(event.target.value);
  };
  const handleSubmit = async () => {
    // console.log(user)
    try {

      if ((user.pinCode === pincode)) {
        setMismatch(false);
        onSuccess();
      }
      else {
        setMismatch(true);
      }
    }
    catch (err) {
      console.log(err);
    }

  };

  return (
    <Dialog
      aria-describedby="alert-dialog-slide-description"
      fullWidth
      scroll={'body'}
      maxWidth={'xs'}
      onClose={onModalClose}
      {...other}

    >
      <Card sx={{ bgcolor: 'primary.dark', p: 3 }}>
        <Stack spacing={2} direction={'row'} alignItems={'center'} justifyContent={'center'} color={'text.secondary'}>
          <Iconify icon={'ic:round-security'} width={24} height={24} />
          <Typography variant={'h4'} >
            Input your security code
          </Typography>
        </Stack>

        <IconButton
          sx={{ position: 'absolute', right: 10, top: 10, zIndex: 1 }}
          onClick={onModalClose}
        >
          <Iconify icon={'eva:close-fill'} width={30} height={30} />
        </IconButton>
        <Divider sx={{ mb: 3 }} />
        <Stack spacing={2} justifyContent={'center'}>
          <TextField
            type="password"
            label="current security code"
            onChange={handleOldPin}
          />
          {mismatch && <Alert severity="error"> Pin code mismatch </Alert>}{' '}
          <Button
            sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
            variant={'contained'}
            fullWidth
            onClick={handleSubmit}
          >
            Confirm Security Code
          </Button>
        </Stack>
      </Card >

    </Dialog>
  );
}





