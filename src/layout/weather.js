import React, { useState, useEffect } from 'react';

const WeatherDisplay = ({ simpleMode = false }) => {
    const [weatherData, setWeatherData] = useState(null);
    const apiKey = 'fb2157619a394f40aa180455230212';
    const city = 'Ulaanbaatar';
  useEffect(() => {
    const fetchWeatherData = async () => {
      try {
        const response = await fetch(
          `https://api.weatherapi.com/v1/current.json?key=${apiKey}&q=${city}`
        );

        if (response.ok) {
          const data = await response.json();
          setWeatherData(data);
        } else {
          // Handle error
          // console.error('Failed to fetch weather data');
        }
      } catch {
        // Handle error
        // console.error('Error fetching weather data:', error);
      }
    };

    fetchWeatherData();
  }, [apiKey, city]);
  if (simpleMode && weatherData && weatherData.current) {
    // Return only the temperature value
    return <span>{weatherData.current.temp_c}°C</span>;
  }
  return (
    <div style={{ position: 'absolute', left: '50%', transform: 'translateX(-50%)', backgroundColor: 'transparent', width: '100%', textAlign: 'center' }}>
    {weatherData && weatherData.current && (
      <div style={{ maxWidth: '300px', color: 'white', fontSize: '0.9em', borderRadius: '8px', padding: '10px', backgroundColor: 'rgba(51, 51, 51, 0.8)', margin: '0 auto' }}>
        <p style={{ fontSize: '1.0em', marginBottom: '8px' }}>{city} - {new Date().toLocaleDateString(undefined, { month: 'long', day: 'numeric' })}</p>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '0.8em' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span role="img" aria-label="condition-icon" style={{ marginRight: '5px' }}>🌞</span>
            Нөхцөл: {weatherData.current.condition?.text || 'N/A'}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', fontSize: '1.2em' }}>
            <span role="img" aria-label="temperature-icon" style={{ marginRight: '5px' }}>🌡️</span>
            Гадаа хэм: {weatherData.current.temp_c !== undefined ? `${weatherData.current.temp_c}°C` : 'N/A'}
          </div>
        </div>
        {/* Additional weather data can be added here */}
      </div>
    )}
  </div>
  );
};

export default WeatherDisplay;
