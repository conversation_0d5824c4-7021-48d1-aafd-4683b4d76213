


import { <PERSON><PERSON>,  <PERSON>rid,  Con<PERSON>er, <PERSON><PERSON><PERSON>, Di<PERSON>r, TextField, Button, Chip, Select, MenuItem, FormControl, InputLabel } from '@mui/material';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// hooks
import useAuth from '../hooks/useAuth';
// components
import Page from '../components/Page';
import axios from '../utils/axios';
import Layout from '../layout';
import { fDate } from '../utils/formatUtils';
import { PRICE_PER_MONTH } from '../config';
import { fShortenNumber } from '../utils/formatUtils';
import { PaymentDialog } from './PaymentDialog';

// ----------------------------------------------------------------------

export default function DeviceProfile() {
  const { initialize, user } = useAuth();
  const [qrImage,setQrImage] = useState();
  const [bankList, setBankList] = useState([]);
  const [paymentRequest, setPaymentRequest] = useState(false);
  const [totalPrice, setTotalPrice] = useState(PRICE_PER_MONTH);
  const { t } = useTranslation();

  const [days, setDays] = useState(30); // Default to 30 days

  const handleLicenseKey = async () => {
    const response = await axios.post(`/api/license/extend-license`, { totalCost: totalPrice });
    if (response.status === 200) {
      if (response.data.data && response.data.data.bankList) {
        setQrImage(response.data.data.bankList.qr_image)
        setBankList(response.data.data.bankList.urls);
        setPaymentRequest(true);
      }
    }
  }

  const handleChange = (e) => {
    setDays(parseInt(e.target.value, 10));
    if (e.target.value === '12') {
      setTotalPrice(7 * PRICE_PER_MONTH); // Set total price for 1 year to 7 months payment
    } else {
      setTotalPrice((e.target.value * PRICE_PER_MONTH));
    }
  }

  useEffect(() => {
    
  }, []);
  
  return (
    <Page title="Device Profile">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
        <Grid container spacing={3}  >

          <Grid item xs={12} >
            <Typography variant='h4' sx={{ mt: 2 }}>
              {t("device_profile.license_information")}
              <Chip sx={{ ml: 2 }} label={user?.status}
                size="small" />
            </Typography>
            <Divider sx={{ mb: 4, mt: 1 }} />

            <Stack spacing={3}>

              <TextField label={`${t("words.license")}`} disabled value={user.licenseKey} />
              <TextField label={`${t("words.expired")}`} disabled value={fDate(user.expired)} />
              <FormControl>
                <InputLabel id="period-select-label">{t("words.period")}</InputLabel>
                <Select label="Period" onChange={handleChange} value={`${days}`} labelId="period-select-label">
                  <MenuItem value='1' >1 Month</MenuItem>
                  <MenuItem value='3'>3 Months</MenuItem>
                  <MenuItem value='6'>6 Months</MenuItem>
                  <MenuItem value='12'>1 Year</MenuItem>
                  <MenuItem value='36'> Forever </MenuItem>
                </Select>
              </FormControl>
              <Typography sx={{ textAlign: 'right' }}>{t("device_profile.total_price")}: {fShortenNumber(totalPrice)}</Typography>
              <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                onClick={handleLicenseKey} variant="contained"  >
                {t("device_profile.request_license")}
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </Container>
      {paymentRequest && <PaymentDialog qrImage={qrImage} open={paymentRequest} onClose={() => { initialize(); setPaymentRequest(false); }} bankList={bankList} />}
    </Page>
  );
}
