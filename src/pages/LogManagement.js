import { useState, useEffect } from 'react';
// @mui

import {
    Card,
    Table,

    Checkbox,
    TableRow,
    TableBody,
    TableCell,
    Container,
    TableContainer,
    TablePagination,
    Divider,
    Grid,
    Skeleton
} from '@mui/material';
// components
import Page from '../components/Page';
import Scrollbar from '../components/Scrollbar';
import SearchNotFound from '../components/SearchNotFound';
import { getYMDHISDate } from '../utils/formatUtils';

// sections
import { LogListHead, LogListToolbar, LogMoreMenu } from "../sections/log";

import axios from "../utils/axios";
import Layout from '../layout';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
    { id: 'deviceNumber', label: 'device_log.device_number' },
    { id: 'command', label: 'device_log.command' },
    { id: 'sent', label: 'device_log.sent' },
    { id: 'response', label: 'device_log.response' },
    { id: 'sentTime', label: 'device_log.time_of_sent' },
    { id: 'receiveTime', label: 'device_log.time_of_receive' },
    { id: 'message', label: 'device_log.response_message' },
    { id: 'responseType', label: 'device_log.http_or_hook' },
    { id: '' },
];

// ----------------------------------------------------------------------


export default function LogManagement() {
    const [loading, setLoading] = useState(false);
    const [logList, setLogList] = useState([]);
    const [page, setPage] = useState(0);
    const [order, setOrder] = useState('asc');
    const [selected, setSelected] = useState([]);
    const [orderBy, setOrderBy] = useState('deviceNumber');
    const [filterName, setFilterName] = useState('');
    const [rowsPerPage, setRowsPerPage] = useState(10);

    const handleRequestSort = (property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };

    const handleSelectAllClick = (event) => {
        if (event.target.checked) {
            const newSelecteds = filteredLogs.splice(0, Math.min(rowsPerPage,logList.length)).map((n)=>n._id);
            // console.log(newSelecteds);
            // const newSelecteds = logList.map((n) => n._id).splice(0,Math.min(rowsPerPage,logList.length)); //  n.name);
            setSelected(newSelecteds);
            return;
        }
        setSelected([]);
    };

    const handleClick = (_id) => {
        const selectedIndex = selected.indexOf(_id);
        let newSelected = [];
        if (selectedIndex === -1) {
            newSelected = newSelected.concat(selected, _id);
        } else if (selectedIndex === 0) {
            newSelected = newSelected.concat(selected.slice(1));
        } else if (selectedIndex === selected.length - 1) {
            newSelected = newSelected.concat(selected.slice(0, -1));
        } else if (selectedIndex > 0) {
            newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
        }
        setSelected(newSelected);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleFilterByName = (filterName) => {
        setFilterName(filterName);
        setPage(0);
    };

    const handleDeleteLog = async (_id) => {
        const deleteLog = logList.filter((log) => log._id !== _id);
        const res = await axios.post('/api/log/delete', { ids: [_id] });
        if (res.status === 200 && res.data.success) {
            setSelected([]);
            setLogList(deleteLog);
        }

    };

    const handleDeleteMultiLog = async (selected) => {
        const deleteLogs = logList.filter((log) => !selected.includes(log._id));
        const res = await axios.post('/api/log/delete', { ids: selected });
        if (res.status === 200 && res.data.success) {
            setSelected([]);
            setLogList(deleteLogs);
        }
    };

    useEffect(() => {
        setLoading(true)
        axios.post('/api/log/list').then(res => {

            setLogList(res.data.logs);
        })
            .catch(err => {

            }).finally(() => setLoading(false))
    }, []);
    const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - logList.length) : 0;

    const filteredLogs = applySortFilter(logList, getComparator(order, orderBy), filterName);

    const isNotFound = !filteredLogs.length && Boolean(filterName);
    const handleChangeTime = (time) => {

    }
    const [viewMap, setViewMap] = useState(false);
    const [deviceStatus, setDeviceStatus] = useState({});
    const handleLocation = (_id) => {
        try {
            const log = logList.filter((log) => log._id === _id)[0];

            if (log.receiveTime && log.response && log.message.indexOf("Lon") !== -1) {
                setDeviceStatus(JSON.parse(log.message));
                setViewMap(true);
            }
            else {
                setViewMap(false);
            }
        }
        catch (err) {
            console.log(err);
        }
    }
    return (
        <Page title="Log Management">
            <Container sx={{ py: { xs: 12 } }}>
                <Layout />
                <Card sx={{ mb: 2, p: 2 }}>
                    <LogListToolbar
                        numSelected={selected.length}
                        filterName={filterName}
                        onFilterName={handleFilterByName}
                        onDeleteDevice={() => handleDeleteMultiLog(selected)}
                        onChangeTime={handleChangeTime}
                    />
                    <Divider />
                    <Scrollbar>
                        {loading &&
                            [1, 2, 3, 4, 5].map((index) => (
                                <Skeleton height={40} key ={index}/>
                            ))
                        }
                        {!loading &&
                            <TableContainer  >
                                <Table  stickyHeader >
                                    <LogListHead
                                        order={order}
                                        orderBy={orderBy}
                                        headLabel={TABLE_HEAD}
                                        rowCount={logList.length}
                                        numSelected={selected.length}
                                        onRequestSort={handleRequestSort}
                                        onSelectAllClick={handleSelectAllClick}
                                    />
                                    <TableBody>
                                        {filteredLogs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row) => {
                                            const { _id, deviceNumber, command, responseType, sent, response, sentTime, receiveTime, message } = row;
                                            const isItemSelected = selected.indexOf(_id) !== -1;

                                            return (
                                                <TableRow
                                                    
                                                    key={_id}
                                                    tabIndex={-1}
                                                    role="checkbox"
                                                    selected={isItemSelected}
                                                    aria-checked={isItemSelected}
                                                >
                                                    <TableCell padding="checkbox">
                                                        <Checkbox checked={isItemSelected} onClick={() => handleClick(_id)} />
                                                    </TableCell>
                                                    <TableCell align="left">{deviceNumber || " "}</TableCell>
                                                    <TableCell align="left">{command || " "}</TableCell>
                                                    <TableCell align="left">{sent || " "}</TableCell>
                                                    <TableCell align="left">{response || " "}</TableCell>
                                                    <TableCell align="left">{getYMDHISDate(sentTime) || " "}</TableCell>
                                                    <TableCell align="left">
                                                        {getYMDHISDate(receiveTime) || " "}
                                                    </TableCell>
                                                    <TableCell align="left">
                                                        {message || " "}
                                                    </TableCell>
                                                    <TableCell align="left">
                                                        {responseType || " "}
                                                    </TableCell>
                                                    <TableCell align="right">
                                                        <LogMoreMenu id={_id} onDelete={() => handleDeleteLog(_id)} onLocation={() => handleLocation(_id)} />
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                        {emptyRows > 0 && (
                                            <TableRow style={{ height: 53 * emptyRows }}>
                                                <TableCell colSpan={10} />
                                            </TableRow>
                                        )}
                                    </TableBody>
                                    {isNotFound && (
                                        <TableBody>
                                            <TableRow>
                                                <TableCell align="center" colSpan={10} sx={{ py: 3 }}>
                                                    <SearchNotFound searchQuery={filterName} />
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    )}
                                </Table>
                            </TableContainer>
                        }
                    </Scrollbar>
                    <TablePagination
                        rowsPerPageOptions={[5, 10, 25, 50]}
                        component="div"
                        count={filteredLogs.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={(e, page) => setPage(page)}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                </Card>

                {viewMap && (
                    <Grid item xs={12} >
                        <iframe width="100%" height="400"
                            src={`https://maps.google.com/maps?q=
                  ${deviceStatus?.Lat || '46'}N,
                  ${deviceStatus?.Lon || '103'}E
                  &t=k
                  &z=15
                  &center=${deviceStatus?.Lat || '46'},${deviceStatus?.Lon || '103'}
                  &ie=UTF8
                  &iwloc=near
                  &output=embed`}
                            frameBorder="0" scrolling="no" marginHeight="0" marginWidth="0" title="device location" />


                        {/* <CarLocation lat={deviceStatus?.Lat} lng={deviceStatus?.Lon} /> */}

                    </Grid>
                )}

            </Container>
        </Page>
    );
}

// ----------------------------------------------------------------------

function descendingComparator(a, b, orderBy) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}

function getComparator(order, orderBy) {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
    const stabilizedThis = array.map((el, index) => [el, index]);
    stabilizedThis.sort((a, b) => {
        const order = comparator(a[0], b[0]);
        if (order !== 0) return order;
        return a[1] - b[1];
    });
    if (query) {
        return array.filter((_Log) => { return (_Log.deviceNumber.indexOf(query.toLowerCase()) !== -1 || (_Log.receiveTime && _Log.receiveTime.indexOf(query.toLowerCase()) !== -1)); });
    }
    return stabilizedThis.map((el) => el[0]);
}
