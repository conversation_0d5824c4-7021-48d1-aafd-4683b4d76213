import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, Container, FormControl, Grid, InputLabel, MenuItem, Select, Stack, TextField, Typography } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import { Icon } from "@iconify/react";
import * as L from "leaflet";
import 'leaflet/dist/leaflet.css';
import { useMemo, useState } from "react";
import Page from "../components/Page";
import Layout from "../layout";
import axios from "../utils/axios";
import { IconButtonAnimate } from "../components/animate";
import { useEffect , useCallback} from "react";
import { formatSQLDate, fTimer } from "../utils/formatUtils";
import useAuth from "../hooks/useAuth";
import { t } from "i18next";
import { fShortenNumber } from "../utils/formatUtils";
import "leaflet-routing-machine";
// import RoutingMachine from "../sections/maps/RoutineMachine";

function createIcon(url) {
    return new L.Icon({
        iconUrl: url,
        iconSize: [20, 20]
    });
}
export default function GpsHistoryOnMap() {

    const [from, setFrom] = useState(formatSQLDate(new Date(new Date().getTime() - 24 * 15 * 60 * 60 * 1000)));
    const [to, setTo] = useState(formatSQLDate(new Date()));
    const [lat] = useState(47.8994);
    const [lng] = useState(106.8994);
    const [log, setLog] = useState([]);
    const [indexPoint, setIndexPoint] = useState({ lat: 47.89994, lng: 106.8994 });
    const [selectedLog, setSelectedLog] = useState();
    const { user } = useAuth();
    const [deviceNumber, setDeviceNumber] = useState(user?.device?.deviceNumber);
    const [routines, setRoutines] = useState();
    const getMarkerIcon = (index) => {

        return createIcon("/images/red-point.png");
    }

    const MapHook = ({ location }) => {
        const map = useMap();
        if (location.lat && location.lng)
            map.setView([location.lat, location.lng], 13)
        return null;
    }

    const MapRoutes = ({ routines }) => {
        const map = useMap();
        if (routines && routines.length > 0) {
            L.Routing.control({
                waypoints: routines
                ,
                lineOptions: {
                    styles: [{ color: "blue", weight: 4 }]
                },
                show: false,
                addWaypoints: false,
                routeWhileDragging: true,
                draggableWaypoints: true,
                fitSelectedRoutes: true,
                showAlternatives: false,
                createMarker: function () {
                    return null;
                }
            }).addTo(map)
        }
        return null;
    }


    const load = useCallback(() => {
        axios.post('/api/log/gps-get-by-date', { from, to, deviceNumber }).then(
            (res) => {
                console.log(res.data);
                if (res.status === 200) {
                    setLog(res.data.logs);
                    if (res.data.logs.length > 0)
                        setIndexPoint({ lat: res.data.logs[0].lat, lng: res.data.logs[0].lng });
                    if (res.data.logs.length > 1) {
                        const points = [];
                        points.push(res.data.logs.map((log) => (L.latLng(log.lat, log.lng))));
                        setRoutines(points[0]);
                    }
                }
            }
        );
    }, [from, to, deviceNumber]);
    
    useEffect(() => {
        if (deviceNumber !== '') load();
    }, [deviceNumber, load]); // Include 'load' as a dependency
    const eventHandler = useMemo(
        () => ({
            click(e) {
                try {
                    const l = log.filter(l => (`${l._id}` === `${e.target.options.id}`))[0];
                    setSelectedLog(l)
                }
                catch (err) {
                    console.log(err)
                }
            },
        }),
        [log],
    )

    return (
        <Page title="Device Profile">
            <Container sx={{ py: { xs: 10 } }}>
                <Layout />

                <Stack >
                    <Stack p={1} alignItems={'center'} justifyContent={'center'}>
                        <FormControl sx={{ width: '340px', flexGrow: 1 }}>
                            <InputLabel id="type-select-label">{t("words.device")}</InputLabel>
                            <Select label={t("words.device")} value={deviceNumber} onChange={(e) => (setDeviceNumber(e.target.value))} labelId="type-select-label">
                                {user?.devices?.filter(d => d.uix === 'GPS')?.map((dn, index) => (
                                    <MenuItem value={dn.deviceNumber} key={index}>{dn.deviceNumber}</MenuItem>
                                ))}

                            </Select>
                        </FormControl>
                    </Stack>
                    <Stack direction="row" justifyContent={'center'} alignItems={'center'} gap={1} py={1}>
                        <TextField size="small" defaultValue={from} onChange={(e) => { setFrom(e.target.value) }}></TextField>
                        <Typography>-</Typography>
                        <TextField size="small" defaultValue={to} onChange={(e) => { setTo(e.target.value) }}></TextField>


                        <IconButtonAnimate onClick={() => load()}
                            sx={
                                {
                                    p: 0,

                                }
                            } >
                            <Icon icon={'ei:refresh'} width={30}></Icon>
                        </IconButtonAnimate>
                    </Stack>
                    <Stack sx={{ mb: 2 }}>
                        <MapContainer center={[lat, lng]} zoom={13} style={{ height: "120vh" }}>
                            <TileLayer
                                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                                attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
                            />
                            {log?.map((l, index) => {
                                return (
                                    <Marker id={l._id} eventHandlers={eventHandler} key={index} position={{ lat: l.lat, lng: l.lng }} icon={getMarkerIcon(0)} >

                                        <Tooltip permanent direction="bottom" offset={[0, 10]}>
                                            <Stack  >
                                                <Typography variant="caption" color={'red'}>{index + 1}</Typography>
                                                <Typography variant="caption">{formatSQLDate(l?.createdAt)}</Typography>
                                                <Typography variant="caption">{fTimer(l?.createdAt)}</Typography>
                                                <Typography variant="caption">battery:{l.battery}mv</Typography>
                                            </Stack>
                                        </Tooltip>
                                    </Marker>
                                )
                            }
                            )}
                            <MapHook location={selectedLog ? { lat: selectedLog.lat, lng: selectedLog.lng } : indexPoint} />
                            <MapRoutes routines={routines} />
                        </MapContainer>
                    </Stack>
                    {selectedLog &&
                        <Stack justifyContent={'center'} p={0} gap={2} alignItems={'center'}>
                            <Card sx={{ width: '100%' }}>
                            <CardHeader title={`${formatSQLDate(selectedLog?.createdAt)} ${fTimer(selectedLog?.createdAt)}`} />
                                <CardContent sx={{ px: 1 }}>

                                    <Typography sx={{ mb: 1 }}>Battery: {selectedLog?.battery}mv</Typography>
                                    <Grid container>
                                        <Grid item xs={1} >
                                            <Typography>Ble </Typography>
                                        </Grid>
                                        <Grid item xs={2}>
                                            <Typography>Door</Typography>
                                        </Grid>
                                        <Grid item xs={2}>
                                            <Typography>Shock</Typography>
                                        </Grid>
                                        <Grid item xs={2}>
                                            <Typography>Hum</Typography>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <Typography>Temp</Typography>
                                        </Grid>
                                        <Grid item xs={2}>
                                            <Typography>Bright</Typography>
                                        </Grid>
                                    </Grid>
                                    {selectedLog?.bles?.filter(ble => ble !== null && ble !== 'null')?.map((ble, index) => (

                                        <Grid container key={index}>
                                            <Grid item xs={1} >
                                                <Typography>{index + 1}</Typography>
                                            </Grid>
                                            <Grid item xs={2}>
                                                <Typography>{ble.door}</Typography>
                                            </Grid>
                                            <Grid item xs={2}>
                                                <Typography>{ble.shock}</Typography>
                                            </Grid>
                                            <Grid item xs={2}>
                                                <Typography>{fShortenNumber(ble.humidity)}</Typography>
                                            </Grid>
                                            <Grid item xs={3}>
                                                <Typography>{fShortenNumber(ble.temperature)}C</Typography>
                                            </Grid>
                                            <Grid item xs={2}>
                                                <Typography>{ble.brightness}</Typography>
                                            </Grid>
                                        </Grid>
                                    ))}

                                </CardContent>
                            </Card>
                        </Stack>
                    }
                </Stack>
            </Container>
        </Page >
    )
}
