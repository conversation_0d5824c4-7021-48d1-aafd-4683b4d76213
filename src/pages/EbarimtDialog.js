import { useState, useEffect } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import QRcode from "react-qr-code";
import ListItemText from '@mui/material/ListItemText';
import DialogTitle from '@mui/material/DialogTitle';
import Dialog from '@mui/material/Dialog';
import { Skeleton, Typography, Grid, Button } from '@mui/material';
import axios from '../utils/axios';
import Iconify from '../components/Iconify';


const fullWidth = true;
export default function EbarimtDialog(props) {
    const { onClose, invoiceID, open } = props;
    const [loading, setLoading] = useState(true);
    const handleClose = () => {
        onClose();
    };
    const [ebarimt, setEbarimt] = useState({});

    useEffect(() => {
        axios.post('/api/hook/payment/ebarimt', { invoice_id: invoiceID }).then(

            (res) => {
                setLoading(false);
                console.log(res);
                if (res.status === 200) {

                    setEbarimt(res.data.data.ebarimt.data);
                }
            }
        );
    }, [invoiceID]);
    return (

        <Dialog onClose={handleClose} open={open} fullWidth={fullWidth} maxWidth={'sm'} >
            <DialogTitle>
                <Grid container justifyContent={'center'}>
                <Iconify
                    icon="arcticons:ebarimt"
                    width={80}
                    height={80}
                    sx={{ fontWeight: 700 }}
                />
                <Typography sx={{mt:4, ml:2, fontSize:'1rem'}} >EBARLIMT.MN</Typography>
                </Grid>
                
            </DialogTitle>
            <Grid container>
                <Grid item xs={12}>
                    <List sx={{ pt: 0, }}>
                        <ListItem >
                            <ListItemText primary='Created' secondary={loading ? (<Skeleton variant="rounded" width={'100%'} height={20} animation="wave" />) : ebarimt.created_date} />
                        </ListItem>
                    </List>
                </Grid>

                <Grid item xs={6} sm={6}>

                    <List sx={{ pt: 0, }}>
                        <ListItem>
                            <ListItemText primary='Amount' secondary={loading ? (<Skeleton variant="rounded" width={'100%'} height={20} animation="wave" />) : ebarimt.amount} />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary='CTA' secondary={loading ? (<Skeleton variant="rounded" width={'100%'} height={20} animation="wave" />) : ebarimt.city_tax_amount} />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary='VTA' secondary={loading ? (<Skeleton variant="rounded" width={'100%'} height={20} animation="wave" />) : ebarimt.vat_amount} />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary='Branch' secondary={loading ? (<Skeleton variant="rounded" width={'100%'} height={20} animation="wave" />) : ebarimt.merchant_branch_code} />
                        </ListItem>
                    </List>
                </Grid>
                <Grid item xs={6} sm={6} sx={{ textAlign: "center" }} gap = {2}>
                   
                    <Typography variant='h4' >Lottery:</Typography>
                    <Typography variant='h6'>{ebarimt?.ebarimt_lottery}</Typography>
                 
                    <QRcode value={`${ebarimt?.ebarimt_qr_data}`} size={150} sx={{ textAlign: "center" }} />
                    
                   
                </Grid>
                <Grid item xs={12} sx = {{textAlign:'center',paddingBottom:2}} >
                <Button onClick = {onClose} variant = {'contained'}  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}>Close</Button>
                </Grid>
            </Grid>
        </Dialog>

    );


}