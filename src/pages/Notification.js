import {  Container, List, Stack } from "@mui/material";
import { useEffect } from "react";
import Layout from "../layout";
import Page from "../components/Page";
import axios from "../utils/axios";
import { dispatch } from "../redux/store";
import { getNotifications } from "../redux/slices/notification";
import Scrollbar from "../components/Scrollbar";
import { useSelector } from "react-redux";
import { NotificationItem } from "src/layout/NotificationPopover";
import { LoadingButton } from "@mui/lab";
import { t } from "i18next";

export default function Notification() {
  const { notifications } = useSelector((state) => state.notification);
  useEffect(() => {
    dispatch(getNotifications());
  }, []);

  const onRemoveAll = async () => {
    await axios.post(`/api/log/remove-all-sim-log`);
    dispatch(getNotifications());
  };

  const onReadAll = async () => {
    await axios.post(`/api/log/read-all-sim-log`);
    dispatch(getNotifications());
  };
  const onRemove = (notification) => {
    axios.delete(`/api/log/remove-sim-log/${notification._id}`).then((res) => {
      dispatch(getNotifications());
    });
  };

  const onRead = (notification) => {
    axios
      .post(`/api/log/read-sim-log`, { id: notification._id })
      .then((res) => {
        dispatch(getNotifications());
      });
  };
  return (
    <Page title="">
      <Container sx={{ py: { xs: 12 } }}>
        <Layout />
        <Stack>
          <Stack direction={"row"} justifyContent={"center"} gap={1}>
            <LoadingButton
              
              size="large"
              sx={{
                bgcolor: "grey.50016",
                border: "1px solid",
                borderColor: "grey.50048",
              }}
              onClick={() => {
                onReadAll();
              }}
              variant="contained"
            >
              {t("words.read_all")}
            </LoadingButton>
            <LoadingButton
              
              size="large"
              sx={{
                bgcolor: "grey.50016",
                border: "1px solid",
                borderColor: "grey.50048",
              }}
              onClick={() => {
                onRemoveAll();
              }}
              variant="contained"
            >
              {t("words.remove_all")}
            </LoadingButton>
          </Stack>
          <Scrollbar sx={{ height: "100%" }}>
            <List>
              {notifications?.map((n, index) => (
                <NotificationItem
                  key={index}
                  notification={n}
                  removeAction={() => {
                    onRemove(n);
                  }}
                  readAction={() => {
                    onRead(n);
                  }}
                />
              ))}
            </List>
          </Scrollbar>
        </Stack>
      </Container>
    </Page>
  );
}
