import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from 'notistack';
// mqtt
import mqttService from '../services/mqttService';
// @mui
import {
  Container,
  Grid,
  Typography,
  Stack,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
  Box,
  FormHelperText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
// components
import Page from '../components/Page';
import Layout from '../layout';
import Iconify from '../components/Iconify';
import IconButtonAnimate from '../components/animate/IconButtonAnimate';
// hooks
import useAuth from '../hooks/useAuth';
import useLocalStorage from '../hooks/useLocalStorage'; // Import useLocalStorage hook

// Add these helper functions at the top of your component
const maskDeviceNumber = (deviceNumber) => {
  if (!deviceNumber) return '';

  // If device number is 6 digits or less, just show it
  if (deviceNumber.length <= 6) return deviceNumber;

  // Otherwise, mask all but the last 6 digits
  const lastSix = deviceNumber.slice(-6);
  const maskedPart = '*'.repeat(deviceNumber.length - 6);
  return `${maskedPart}${lastSix}`;
};

const maskBrokerAddress = (address) => {
  if (!address) return '';

  // Split by dots to get IP segments
  const segments = address.split('.');

  // If not an IP address format, return as is
  if (segments.length !== 4) return address;

  // Mask first two segments
  return `****.****.${segments[2]}.${segments[3]}`;
};

export default function DeviceConfig() {
  const { id } = useParams();
  const { user } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const [loading, setLoading] = useState({
    sound: false,
    key: false,
    server: false,
    protocol: false,
    shutdown: false,
    auto_shutdown: false,
    log_level: false,
    update: false,
    restart: false,
    geely_atlas: false,
    voltage_offset: false,
    notifications: false,
    voltage_threshold: false,
    gps: false,
    logs: false,
    charged: false,
    transfer: false
  });

  // Device configuration states
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [keyEnabled, setKeyEnabled] = useState(true);
  const [serverIndex, setServerIndex] = useState(1);
  const [autoShutdownTimer, setAutoShutdownTimer] = useState(0);
  const [currentDeviceNumber] = useState(id);

  // Use local storage for protocol setting
  const [protocol, setProtocol] = useLocalStorage(`device_${id}_protocol`, 'http');

  // Add these state variables at the top with other state declarations
  const [mqttConnected, setMqttConnected] = useState(false);
  const [brokerAddress, setBrokerAddress] = useState('');
  const [autoShutdownEnabled, setAutoShutdownEnabled] = useState(true);
  const [logLevel, setLogLevel] = useState('info');
  const [deviceVersion, setDeviceVersion] = useState('N/A');
  const [geelyAtlasMode, setGeelyAtlasMode] = useState(false);
  const [voltageOffset, setVoltageOffset] = useState(0);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [voltageThreshold, setVoltageThreshold] = useState(0.6);
  const [gpsEnabled, setGpsEnabled] = useState(true);
  const [deviceLogs, setDeviceLogs] = useState('');
  const [logsTimeoutId, setLogsTimeoutId] = useState(null);
  const [logsDialogOpen, setLogsDialogOpen] = useState(false);
  const [transferUnits, setTransferUnits] = useState('');
  const [transferPhoneNumber, setTransferPhoneNumber] = useState('');

  // Update progress states
  const [updateProgress, setUpdateProgress] = useState({
    isActive: false,
    stage: 'idle', // 'idle', 'sending', 'downloading', 'installing', 'completed', 'failed'
    progress: 0,
    message: '',
    startTime: null
  });
  const [updateTimeoutId, setUpdateTimeoutId] = useState(null);

  // Define connectToMqttBroker function first
  const connectToMqttBroker = useCallback(() => {
    try {
      // Get broker address from user data or use default
      const defaultDevice = user?.devices?.find(d => d.isDefault === true);
      const renterFromDevices = defaultDevice?.renter;
      const renterFromDevice = user?.device?.renter;
      const storedBroker = localStorage.getItem(`device_${currentDeviceNumber}_broker`);

      // Debug logging to see what we're getting from user data
      // console.log('User data for broker selection:', {
      //   defaultDevice: defaultDevice,
      //   renterFromDevices: renterFromDevices,
      //   renterFromDevice: renterFromDevice,
      //   storedBroker: storedBroker,
      //   currentDeviceNumber: currentDeviceNumber
      // });

      // Prioritize the user's device renter field
      const userRenterDomain = renterFromDevices || renterFromDevice;

      // Define fallback brokers in order of preference
      const fallbackBrokers = [
        // User's device renter domain has highest priority (unless overridden by stored preference)
        storedBroker,
        userRenterDomain,
        // Your infrastructure nodes (in order of preference)
        "elec.mn",        // node3 - main domain
        "app.elec.mn",    // node2 - app server
        "bot.elec.mn",    // node1 - bot server
        // Known working IP addresses from your memories
        "*************",
        "************",
        "**************",
        // Public EMQX broker as last resort (known to work)
        "broker.emqx.io"
      ].filter(Boolean); // Remove null/undefined values

      // Try the first available broker
      const brokerAddress = fallbackBrokers[0] || "broker.emqx.io";
      // console.log('Selected broker address:', brokerAddress);
      // console.log('Fallback brokers list:', fallbackBrokers);
      setBrokerAddress(brokerAddress);

      // Try WSS first, but fallback to WS if needed
      // Check if we should try non-secure WebSocket (for testing)
      const useSecure = localStorage.getItem(`device_${currentDeviceNumber}_use_wss`) !== 'false';
      const protocol = useSecure ? 'wss' : 'ws';
      const port = useSecure ? "8084" : "8083"; // WSS uses 8084, WS uses 8083
      const brokerUrl = `${protocol}://${brokerAddress}:${port}/mqtt`;
      // console.log(`MQTT ${protocol.toUpperCase()}: Connecting to broker: ${brokerUrl}`);

      // Create unique client ID
      const clientId = `web_config_${currentDeviceNumber}_${Date.now()}`;

      const options = {
        clientId: clientId,
        username: "admin", // Use admin as username
        password: "public", // Use public as password
        clean: true,
        keepalive: 60,
        reconnectPeriod: 1000,
        connectTimeout: 10000, // Reduced timeout for faster fallback
        rejectUnauthorized: false // Allow self-signed certificates
      };

      // Disconnect existing connection if any
      if (mqttService.isConnected) {
        mqttService.disconnect();
      }

      // Connect to MQTT broker using WSS
      const connectionStarted = mqttService.connect(brokerUrl, options);

      if (connectionStarted) {
        // console.log(`MQTT ${protocol.toUpperCase()}: Connection attempt started to broker: ${brokerUrl}`);

        // Set up a fallback mechanism if this broker fails
        setTimeout(() => {
          if (!mqttService.isConnected && fallbackBrokers.length > 1) {
            // console.log(`MQTT ${protocol.toUpperCase()}: Primary broker failed, trying fallback...`);
            // Store the failed broker and try the next one
            const nextBroker = fallbackBrokers[1];
            if (nextBroker) {
              localStorage.setItem(`device_${currentDeviceNumber}_broker`, nextBroker);
              // Retry connection with next broker
              connectToMqttBroker();
            }
          }
        }, 12000); // Wait a bit longer than connectTimeout

        return true;
      } else {
        // console.error(`MQTT ${protocol.toUpperCase()}: Failed to start connection to broker: ${brokerUrl}`);
        return false;
      }
    } catch {
      // console.error("Error in connectToMqttBroker:", error);
      return false;
    }
  }, [currentDeviceNumber, user?.devices, user?.device?.renter, setBrokerAddress]);

  // Add a useEffect to check connection status periodically and trigger reconnection
  useEffect(() => {
    const checkConnectionStatus = () => {
      if (mqttService && mqttService.isConnected !== mqttConnected) {
        console.log(`MQTT connection status changed: ${mqttService.isConnected}`);
        setMqttConnected(mqttService.isConnected);

        // If we just got connected and have a device number, set up subscriptions
        if (mqttService.isConnected && currentDeviceNumber) {
          const msgTopic = `${currentDeviceNumber}/msg`;
          console.log('Re-subscribing to topics after reconnection');
          mqttService.subscribe(msgTopic);
          mqttService.subscribe(currentDeviceNumber);

          // Send check command after reconnection
          setTimeout(() => {
            sendCheckCommand();
          }, 1000);
        }
      }

      // If disconnected and we have a device number, try to reconnect
      if (mqttService && !mqttService.isConnected && currentDeviceNumber) {
        console.log('MQTT disconnected, attempting reconnection');
        connectToMqttBroker();
      }
    };

    // Check immediately
    checkConnectionStatus();

    // Check every 5 seconds (increased from 2 seconds to reduce load)
    const statusInterval = setInterval(checkConnectionStatus, 5000);

    return () => {
      clearInterval(statusInterval);
    };
  }, [mqttConnected, currentDeviceNumber]);

  // Add this useEffect to handle MQTT connection and send check command
  useEffect(() => {
    // Only attempt connection if we have the necessary data
    if (protocol === 'tcp' && currentDeviceNumber) {
      console.log('DeviceConfig: Setting up MQTT connection for device:', currentDeviceNumber);

      // Set up event listeners for MQTT connection events
      const handleMqttConnected = () => {
        console.log('MQTT WSS connected in DeviceConfig');
        setMqttConnected(true);
        enqueueSnackbar(t('device_config.mqtt_connected'), { variant: 'success' });

        // Subscribe to device's message topic
        const msgTopic = `${currentDeviceNumber}/msg`;
        console.log('Subscribing to message topic:', msgTopic);
        mqttService.subscribe(msgTopic);

        // Also subscribe to the device number topic directly
        console.log('Subscribing to device topic:', currentDeviceNumber);
        mqttService.subscribe(currentDeviceNumber);

        // Send check command to get device status including firmware version
        setTimeout(() => {
          sendCheckCommand();
        }, 1000);
      };

      const handleMqttDisconnected = () => {
        console.log('MQTT WSS disconnected in DeviceConfig');
        setMqttConnected(false);
        enqueueSnackbar(t('device_config.mqtt_disconnected'), { variant: 'warning' });
      };

      const handleMqttConnectionFailed = () => {
        console.log('MQTT WSS connection failed in DeviceConfig');
        setMqttConnected(false);
        enqueueSnackbar(t('device_config.mqtt_connection_failed'), { variant: 'error' });
      };

      // Add event listeners
      window.addEventListener('mqtt-connected', handleMqttConnected);
      window.addEventListener('mqtt-disconnected', handleMqttDisconnected);
      window.addEventListener('mqtt-connection-failed', handleMqttConnectionFailed);

      // Check if already connected, if so, just set up subscriptions
      if (mqttService.isConnected) {
        console.log('MQTT already connected, setting up subscriptions');
        handleMqttConnected();
      } else {
        // Initial connection attempt
        console.log('MQTT not connected, attempting connection');
        connectToMqttBroker();
      }

      // Set up periodic reconnection attempts
      const reconnectInterval = setInterval(() => {
        if (!mqttService.isConnected && protocol === 'tcp') {
          console.log('Attempting MQTT WSS reconnection in DeviceConfig');
          connectToMqttBroker();
        }
      }, 10000); // Try to reconnect every 10 seconds

      // Cleanup function
      return () => {
        window.removeEventListener('mqtt-connected', handleMqttConnected);
        window.removeEventListener('mqtt-disconnected', handleMqttDisconnected);
        window.removeEventListener('mqtt-connection-failed', handleMqttConnectionFailed);
        clearInterval(reconnectInterval);
      };
    }
  }, [currentDeviceNumber, protocol, t, enqueueSnackbar]);

  // Add a separate useEffect to handle initial connection on page load/refresh
  useEffect(() => {
    // Force connection attempt after a short delay to ensure all states are initialized
    const initTimer = setTimeout(() => {
      if (currentDeviceNumber && !mqttService.isConnected) {
        console.log('DeviceConfig: Force connection attempt on page load/refresh');
        connectToMqttBroker();
      }
    }, 1000);

    return () => clearTimeout(initTimer);
  }, [currentDeviceNumber]);

  const saveConfiguration = async (configType, value) => {
    // Set loading state for specific config type
    setLoading(prev => ({ ...prev, [configType]: true }));

    try {
      // Check if MQTT WSS is connected
      if (!mqttService.isConnected) {
        // Try to reconnect if not connected
        // console.log("MQTT WSS not connected, attempting to reconnect");
        const connected = connectToMqttBroker();

        // Wait a bit for connection to establish
        if (connected) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Check again if connected
        if (!mqttService.isConnected) {
          enqueueSnackbar(t('device_config.mqtt_connection_failed'), { variant: 'error' });
          setLoading(prev => ({ ...prev, [configType]: false }));
          return;
        }
      }

      // Map config type to actual command
      let commandString = '';
      switch(configType) {
        case 'sound':
          commandString = value ? 'sound on' : 'sound off';
          break;
        case 'key':
          commandString = value ? 'key on' : 'key off';
          break;
        case 'server':
          // Updated format: "setserver serverx" (setserver command with serverx parameter)
          commandString = `setserver server${value}`;
          break;
        case 'protocol':
          commandString = `protocol ${value}`;
          break;
        case 'shutdown':
          // Ensure value is treated as a number and handle empty string
          const timerValue = value === '' ? 0 : parseInt(value, 10);
          commandString = `auto_shutdown timer ${timerValue}`;
          break;
        case 'auto_shutdown':
          commandString = value ? 'auto_shutdown on' : 'auto_shutdown off';
          break;
        case 'log_level':
          commandString = `set log ${value}`;
          break;
        case 'geely_atlas':
          commandString = value ? 'geely_atlas on' : 'geely_atlas off';
          break;
        case 'voltage_offset':
          // Format the voltage offset with one decimal place and ensure it's in the valid range
          const formattedOffset = parseFloat(value).toFixed(1);
          commandString = `volt${formattedOffset}`;
          break;
        case 'notifications':
          commandString = value ? 'notify on' : 'notify off';
          break;
        case 'voltage_threshold':
          // Format the voltage threshold with one decimal place
          const formattedThreshold = parseFloat(value).toFixed(1);
          commandString = `th${formattedThreshold}`;
          break;
        case 'gps':
          commandString = value ? 'gps on' : 'gps off';
          break;
        case 'restart':
          commandString = 'restart';
          break;
        case 'charged':
          commandString = 'charged';
          break;
        case 'transfer':
          // Transfer units command format: "unitel:phoneNumber units" where value is an object with phoneNumber and units
          commandString = `unitel:${value.phoneNumber} ${value.units}`;
          break;
        default:
          commandString = `${configType} ${value}`;
      }

      // Prepare the MQTT message in the correct format
      const topic = currentDeviceNumber;
      const message = JSON.stringify({
        id: currentDeviceNumber,
        command: commandString
      });

      // console.log(`MQTT WSS: Sending command: ${commandString}`);
      // console.log("MQTT WSS topic:", topic);
      // console.log("MQTT WSS message:", message);

      // Send the message via MQTT WSS
      const success = mqttService.publish(topic, message);

      if (success) {
        // Show success message after a delay
        setTimeout(() => {
          enqueueSnackbar(t('device_config.save_success'), { variant: 'success' });
          setLoading(prev => ({ ...prev, [configType]: false }));
        }, 1000);
      } else {
        // console.error("MQTT WSS publish failed");
        enqueueSnackbar(t('device_config.save_error'), { variant: 'error' });
        setLoading(prev => ({ ...prev, [configType]: false }));
      }
    } catch {
      // console.error("Error in saveConfiguration:", error);
      enqueueSnackbar(t('device_config.save_error'), { variant: 'error' });
      setLoading(prev => ({ ...prev, [configType]: false }));
    }
  };

  // Update the MQTT message handler to extract version information
  useEffect(() => {
    // Function to handle incoming MQTT messages
    const handleMqttMessage = (event) => {
      try {
        const message = event.detail;
        // Check if this is a message from our device
        if (message && message.topic && (
            message.topic === currentDeviceNumber ||
            message.topic === `${currentDeviceNumber}/msg` ||
            message.topic === `${currentDeviceNumber}/logs`
        )) {
          let payload;

          // Parse the payload if it's a string
          if (typeof message.payload === 'string') {
            try {
              payload = JSON.parse(message.payload);
            } catch (e) {
              // console.log('Failed to parse payload as JSON, using as-is:', message.payload);
              payload = message.payload;
            }
          } else {
            payload = message.payload;
          }

          // console.log('Parsed payload:', payload);

          // Extract version information if available
          if (payload && payload.ver) {
            // console.log('Found version in payload:', payload.ver);
            setDeviceVersion(payload.ver);
          } else if (payload && typeof payload === 'object') {
            // Try to find version in nested objects or with different key names
            const possibleVersionKeys = ['ver', 'version', 'firmware', 'fw'];
            for (const key of possibleVersionKeys) {
              if (payload[key]) {
                // console.log(`Found version with key ${key}:`, payload[key]);
                setDeviceVersion(payload[key]);
                break;
              }
            }
          }

          // Handle server command success responses
          if (payload && payload.status === "Success" && payload.message &&
              payload.message.includes("Changing to server") && payload.message.includes("Restarting")) {
            // Extract server number from the message (e.g., "Changing to server1. Restarting in 3 seconds...")
            const serverMatch = payload.message.match(/server(\d+)/);
            const serverNumber = serverMatch ? serverMatch[1] : '';

            enqueueSnackbar(
              `Server changed to server${serverNumber}. Device will restart in 3 seconds.`,
              { variant: 'success' }
            );

            // Clear the server loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, server: false }));
            }, 1000);
          }

          // Handle key command responses
          if (payload && payload.key_state && (payload.status?.includes("Key state") || payload.status?.includes("key"))) {
            const keyState = payload.key_state === "on" ? "enabled" : "disabled";

            enqueueSnackbar(
              `Key detection ${keyState}`,
              { variant: 'success' }
            );

            // Update the local state to match the device response
            setKeyEnabled(payload.key_state === "on");

            // Clear the key loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, key: false }));
            }, 1000);
          }

          // Handle auto-shutdown command responses
          if (payload && payload.status && payload.status.includes("Auto-shutdown feature")) {
            const isEnabled = payload.status.includes("enabled");
            const statusText = isEnabled ? "enabled" : "disabled";

            enqueueSnackbar(
              `Auto-shutdown feature ${statusText}`,
              { variant: 'success' }
            );

            // Update the local state to match the device response
            setAutoShutdownEnabled(isEnabled);

            // Clear the auto_shutdown loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, auto_shutdown: false }));
            }, 1000);
          }

          // Handle Geely Atlas mode command responses
          if (payload && payload.status && payload.status.includes("Geely Atlas mode")) {
            const isEnabled = payload.status.includes("enabled");
            const statusText = isEnabled ? "enabled" : "disabled";

            enqueueSnackbar(
              `Geely Atlas mode ${statusText}`,
              { variant: 'success' }
            );

            // Update the local state to match the device response
            setGeelyAtlasMode(isEnabled);

            // Clear the geely_atlas loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, geely_atlas: false }));
            }, 1000);
          }

          // Handle voltage offset command responses
          if (payload && payload.status && payload.status.includes("Voltage offset set to")) {
            // Extract the voltage value from the message (e.g., "Voltage offset set to 0.20")
            const voltageMatch = payload.status.match(/Voltage offset set to ([\d.-]+)/);
            const voltageValue = voltageMatch ? voltageMatch[1] : '';

            enqueueSnackbar(
              `Voltage offset set to ${voltageValue}V`,
              { variant: 'success' }
            );

            // Update the local state to match the device response
            if (voltageValue) {
              setVoltageOffset(parseFloat(voltageValue));
            }

            // Clear the voltage_offset loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, voltage_offset: false }));
            }, 1000);
          }

          // Handle voltage notifications command responses
          if (payload && payload.status && payload.status.includes("Voltage notifications")) {
            const isEnabled = payload.status.includes("enabled");
            const statusText = isEnabled ? "enabled" : "disabled";

            enqueueSnackbar(
              `Voltage notifications ${statusText}`,
              { variant: 'success' }
            );

            // Update the local state to match the device response
            setNotificationsEnabled(isEnabled);

            // Clear the notifications loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, notifications: false }));
            }, 1000);
          }

          // Handle voltage threshold command responses
          if (payload && payload.status && payload.status.includes("Voltage threshold set to")) {
            // Extract the voltage value from the message (e.g., "Voltage threshold set to 0.60")
            const thresholdMatch = payload.status.match(/Voltage threshold set to ([\d.-]+)/);
            const thresholdValue = thresholdMatch ? thresholdMatch[1] : '';

            enqueueSnackbar(
              `Voltage threshold set to ${thresholdValue}V`,
              { variant: 'success' }
            );

            // Update the local state to match the device response
            if (thresholdValue) {
              setVoltageThreshold(parseFloat(thresholdValue));
            }

            // Clear the voltage_threshold loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, voltage_threshold: false }));
            }, 1000);
          }

          // Handle charged command responses
          if (payload && payload.status && payload.status === "license_enabled") {
            enqueueSnackbar(
              'License enabled successfully',
              { variant: 'success' }
            );

            // Clear the charged loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, charged: false }));
            }, 1000);
          }

          // Handle transfer command responses
          if (payload && payload.status === "Success" && payload.message &&
              payload.message.includes("Sent Unitel command")) {
            // Extract phone number and units from the message (e.g., "Sent Unitel command for 89932933 with 100 units")
            const phoneMatch = payload.message.match(/for (\d+) with/);
            const unitsMatch = payload.message.match(/with (\d+) units/);
            const phoneValue = phoneMatch ? phoneMatch[1] : '';
            const unitsValue = unitsMatch ? unitsMatch[1] : '';

            enqueueSnackbar(
              `Амжилттай шилжүүлэгдлээ: ${unitsValue} нэгж ${phoneValue} дугаарт илгээгдлээ`,
              { variant: 'success' }
            );

            // Clear the transfer loading state after a short delay
            setTimeout(() => {
              setLoading(prev => ({ ...prev, transfer: false }));
            }, 1000);
          }

          // Handle logs responses
          if (message.topic === `${currentDeviceNumber}/logs`) {
            // This is a logs response
            const logsContent = typeof payload === 'string' ? payload : JSON.stringify(payload, null, 2);
            setDeviceLogs(logsContent);

            enqueueSnackbar('Device logs received', { variant: 'success' });

            // Open the logs dialog
            setLogsDialogOpen(true);

            // Clear the logs loading state
            setLoading(prev => ({ ...prev, logs: false }));

            // Unsubscribe from logs topic after receiving the logs
            setTimeout(() => {
              console.log('Unsubscribing from logs topic');
              mqttService.unsubscribe(`${currentDeviceNumber}/logs`);
            }, 1000);

            // Clear the timeout if it exists
            if (logsTimeoutId) {
              clearTimeout(logsTimeoutId);
              setLogsTimeoutId(null);
            }
          }

          // Handle update progress responses
          if (updateProgress.isActive && payload) {
            // Check for update-related messages
            const messageText = typeof payload === 'string' ? payload.toLowerCase() :
                               (payload.message || payload.status || payload.error || '').toLowerCase();

            // Check for "already latest version" responses
            const isLatestVersion = messageText.includes('已是最新版本') ||
                                   messageText.includes('latest version') ||
                                   messageText.includes('already up to date') ||
                                   messageText.includes('no update available') ||
                                   (payload.status && payload.status.toLowerCase().includes('update failed') &&
                                    payload.error && payload.error.includes('已是最新版本'));

            // Check for "operation failed" responses (too many attempts)
            const isOperationFailed = messageText.includes('操作失败') ||
                                     messageText.includes('operation failed') ||
                                     messageText.includes('too many attempts') ||
                                     (payload.status && payload.status.toLowerCase().includes('update failed') &&
                                      payload.error && payload.error.includes('操作失败'));

            if (isLatestVersion) {
              // Add a small delay to ensure the state change is visible
              setTimeout(() => {
                setUpdateProgress(prev => ({
                  ...prev,
                  stage: 'latest',
                  progress: 100,
                  message: 'Already latest version'
                }));
              }, 100);

              // Clear timeout and reset after delay
              if (updateTimeoutId) {
                clearTimeout(updateTimeoutId);
                setUpdateTimeoutId(null);
              }

              setTimeout(() => {
                setUpdateProgress({
                  isActive: false,
                  stage: 'idle',
                  progress: 0,
                  message: '',
                  startTime: null
                });
                setLoading(prev => ({ ...prev, update: false }));
              }, 5000);

            } else if (isOperationFailed) {
              setUpdateProgress(prev => ({
                ...prev,
                stage: 'failed',
                progress: 0,
                message: 'Too many attempts - try later' // Clear, direct message
              }));

              // Clear timeout and reset after delay
              if (updateTimeoutId) {
                clearTimeout(updateTimeoutId);
                setUpdateTimeoutId(null);
              }

              setTimeout(() => {
                setUpdateProgress({
                  isActive: false,
                  stage: 'idle',
                  progress: 0,
                  message: '',
                  startTime: null
                });
                setLoading(prev => ({ ...prev, update: false }));
              }, 4000); // Show for 4 seconds for operation failed

            } else if (messageText.includes('update') || messageText.includes('download') ||
                       messageText.includes('install') || messageText.includes('firmware')) {

              if (messageText.includes('download')) {
                setUpdateProgress(prev => ({
                  ...prev,
                  stage: 'downloading',
                  progress: 30,
                  message: t('device_config.update_downloading')
                }));
              } else if (messageText.includes('install')) {
                setUpdateProgress(prev => ({
                  ...prev,
                  stage: 'installing',
                  progress: 70,
                  message: t('device_config.update_installing')
                }));
              } else if (messageText.includes('complete') || messageText.includes('success') ||
                         (payload.status && payload.status.toLowerCase().includes('update successful'))) {
                setUpdateProgress(prev => ({
                  ...prev,
                  stage: 'completed',
                  progress: 100,
                  message: 'Update successful!' // Clear, direct message
                }));

                // Clear timeout and reset after delay
                if (updateTimeoutId) {
                  clearTimeout(updateTimeoutId);
                  setUpdateTimeoutId(null);
                }

                setTimeout(() => {
                  setUpdateProgress({
                    isActive: false,
                    stage: 'idle',
                    progress: 0,
                    message: '',
                    startTime: null
                  });
                  setLoading(prev => ({ ...prev, update: false }));
                }, 3000);
              } else if (messageText.includes('fail') || messageText.includes('error')) {
                setUpdateProgress(prev => ({
                  ...prev,
                  stage: 'failed',
                  progress: 0,
                  message: t('device_config.update_failed')
                }));

                // Clear timeout and reset after delay
                if (updateTimeoutId) {
                  clearTimeout(updateTimeoutId);
                  setUpdateTimeoutId(null);
                }

                setTimeout(() => {
                  setUpdateProgress({
                    isActive: false,
                    stage: 'idle',
                    progress: 0,
                    message: '',
                    startTime: null
                  });
                  setLoading(prev => ({ ...prev, update: false }));
                }, 3000);
              }
            }
          }

          // Fallback: Handle update responses even when progress is not active (but button is still loading)
          if (!updateProgress.isActive && loading.update && payload) {

            // Check for update-related messages
            const messageText = typeof payload === 'string' ? payload.toLowerCase() :
                               (payload.message || payload.status || payload.error || '').toLowerCase();

            // Check for "already latest version" responses
            const isLatestVersion = messageText.includes('已是最新版本') ||
                                   messageText.includes('latest version') ||
                                   messageText.includes('already up to date') ||
                                   messageText.includes('no update available') ||
                                   (payload.status && payload.status.toLowerCase().includes('update failed') &&
                                    payload.error && payload.error.includes('已是最新版本'));

            // Check for "operation failed" responses (too many attempts)
            const isOperationFailed = messageText.includes('操作失败') ||
                                     messageText.includes('operation failed') ||
                                     messageText.includes('too many attempts') ||
                                     (payload.status && payload.status.toLowerCase().includes('update failed') &&
                                      payload.error && payload.error.includes('操作失败'));

            if (isLatestVersion) {

              // Reactivate progress to show the result
              setUpdateProgress({
                isActive: true,
                stage: 'latest',
                progress: 100,
                message: 'Already latest version',
                startTime: Date.now()
              });

              // Clear any existing timeout
              if (updateTimeoutId) {
                clearTimeout(updateTimeoutId);
                setUpdateTimeoutId(null);
              }

              // Show success notification
              enqueueSnackbar(t('device_config.update_already_latest'), { variant: 'info' });

              setTimeout(() => {
                setUpdateProgress({
                  isActive: false,
                  stage: 'idle',
                  progress: 0,
                  message: '',
                  startTime: null
                });
                setLoading(prev => ({ ...prev, update: false }));
              }, 4000);
            } else if (isOperationFailed) {

              // Reactivate progress to show the error
              setUpdateProgress({
                isActive: true,
                stage: 'failed',
                progress: 0,
                message: 'Too many attempts - try later',
                startTime: Date.now()
              });

              // Clear any existing timeout
              if (updateTimeoutId) {
                clearTimeout(updateTimeoutId);
                setUpdateTimeoutId(null);
              }

              // Show error notification
              enqueueSnackbar(t('device_config.update_operation_failed'), { variant: 'error' });

              setTimeout(() => {
                setUpdateProgress({
                  isActive: false,
                  stage: 'idle',
                  progress: 0,
                  message: '',
                  startTime: null
                });
                setLoading(prev => ({ ...prev, update: false }));
              }, 4000);
            } else if (messageText.includes('update') && (messageText.includes('fail') || messageText.includes('error'))) {

              // Reactivate progress to show the error
              setUpdateProgress({
                isActive: true,
                stage: 'failed',
                progress: 0,
                message: 'Update failed',
                startTime: Date.now()
              });

              // Clear any existing timeout
              if (updateTimeoutId) {
                clearTimeout(updateTimeoutId);
                setUpdateTimeoutId(null);
              }

              setTimeout(() => {
                setUpdateProgress({
                  isActive: false,
                  stage: 'idle',
                  progress: 0,
                  message: '',
                  startTime: null
                });
                setLoading(prev => ({ ...prev, update: false }));
              }, 3000);
            }
          }
        }
      } catch {
        // console.error('Error processing MQTT message:', error);
      }
    };

    // Add event listener for MQTT messages
    window.addEventListener('mqtt-message', handleMqttMessage);

    // Cleanup function
    return () => {
      window.removeEventListener('mqtt-message', handleMqttMessage);
    };
  }, [currentDeviceNumber, updateProgress, loading, updateTimeoutId, t, enqueueSnackbar]);

  // Cleanup effect for update timeout
  useEffect(() => {
    return () => {
      if (updateTimeoutId) {
        clearTimeout(updateTimeoutId);
      }
    };
  }, [updateTimeoutId]);

  // Cleanup effect for logs timeout
  useEffect(() => {
    return () => {
      if (logsTimeoutId) {
        clearTimeout(logsTimeoutId);
      }
    };
  }, [logsTimeoutId]);

  // Add a function to send the update command
  const sendUpdateCommand = async () => {
    setLoading(prev => ({ ...prev, update: true }));

    // Initialize update progress
    setUpdateProgress({
      isActive: true,
      stage: 'sending',
      progress: 10,
      message: 'Sending update command...',
      startTime: Date.now()
    });

    try {
      // Check if MQTT WSS is connected
      if (!mqttService.isConnected) {
        // Try to reconnect if not connected
        // console.log("MQTT WSS not connected, attempting to reconnect");
        const connected = connectToMqttBroker();

        // Wait a bit for connection to establish
        if (connected) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Check again if connected
        if (!mqttService.isConnected) {
          enqueueSnackbar(t('device_config.mqtt_connection_failed'), { variant: 'error' });
          setLoading(prev => ({ ...prev, update: false }));
          setUpdateProgress({
            isActive: false,
            stage: 'idle',
            progress: 0,
            message: '',
            startTime: null
          });
          return;
        }
      }

      // Prepare the MQTT message
      const topic = currentDeviceNumber;
      const message = JSON.stringify({
        id: currentDeviceNumber,
        command: "update"
      });

      // Send the message via MQTT WSS
      const success = mqttService.publish(topic, message);

      if (success) {
        enqueueSnackbar(t('device_config.update_initiated'), { variant: 'success' });

        // Update progress to waiting for response
        setUpdateProgress(prev => ({
          ...prev,
          stage: 'downloading',
          progress: 20,
          message: 'Waiting for device response...'
        }));

        // Set timeout for update process (60 seconds for testing, 5 minutes for production)
        const timeoutDuration = process.env.NODE_ENV === 'development' ? 60000 : 300000;
        const timeoutId = setTimeout(() => {
          setUpdateProgress({
            isActive: false,
            stage: 'failed',
            progress: 0,
            message: t('device_config.update_timeout'),
            startTime: null
          });
          setLoading(prev => ({ ...prev, update: false }));
          enqueueSnackbar(t('device_config.update_timeout'), { variant: 'warning' });

          setTimeout(() => {
            setUpdateProgress({
              isActive: false,
              stage: 'idle',
              progress: 0,
              message: '',
              startTime: null
            });
          }, 3000);
        }, timeoutDuration);

        setUpdateTimeoutId(timeoutId);

      } else {
        // console.error("MQTT WSS publish failed");
        enqueueSnackbar(t('device_config.update_failed'), { variant: 'error' });
        setLoading(prev => ({ ...prev, update: false }));
        setUpdateProgress({
          isActive: false,
          stage: 'idle',
          progress: 0,
          message: '',
          startTime: null
        });
      }
    } catch {
      // console.error("Error in sendUpdateCommand:", error);
      enqueueSnackbar(t('device_config.update_failed'), { variant: 'error' });
      setLoading(prev => ({ ...prev, update: false }));
      setUpdateProgress({
        isActive: false,
        stage: 'idle',
        progress: 0,
        message: '',
        startTime: null
      });
    }
  };

  // Add a function to send the restart command
  const sendRestartCommand = async () => {
    setLoading(prev => ({ ...prev, restart: true }));

    try {
      // Check if MQTT WSS is connected
      if (!mqttService.isConnected) {
        // Try to reconnect if not connected
        // console.log("MQTT WSS not connected, attempting to reconnect");
        const connected = connectToMqttBroker();

        // Wait a bit for connection to establish
        if (connected) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Check again if connected
        if (!mqttService.isConnected) {
          enqueueSnackbar(t('device_config.mqtt_connection_failed'), { variant: 'error' });
          setLoading(prev => ({ ...prev, restart: false }));
          return;
        }
      }

      // Prepare the MQTT message
      const topic = currentDeviceNumber;
      const message = JSON.stringify({
        id: currentDeviceNumber,
        command: "restart"
      });

      // console.log("MQTT WSS: Sending restart command");
      // console.log("MQTT WSS topic:", topic);
      // console.log("MQTT WSS message:", message);

      // Send the message via MQTT WSS
      const success = mqttService.publish(topic, message);

      if (success) {
        enqueueSnackbar(t('device_config.restart_initiated'), { variant: 'success' });
        setTimeout(() => {
          setLoading(prev => ({ ...prev, restart: false }));
        }, 2000);
      } else {
        // console.error("MQTT WSS publish failed");
        enqueueSnackbar(t('device_config.restart_failed'), { variant: 'error' });
        setLoading(prev => ({ ...prev, restart: false }));
      }
    } catch {
      // console.error("Error in sendRestartCommand:", error);
      enqueueSnackbar(t('device_config.restart_failed'), { variant: 'error' });
      setLoading(prev => ({ ...prev, restart: false }));
    }
  };

  // Add a function to send the check command
  const sendCheckCommand = () => {
    if (!mqttService.isConnected) {
      // console.log("MQTT WSS not connected, cannot send check command");
      return;
    }

    // Prepare the MQTT message
    const topic = currentDeviceNumber;
    const message = JSON.stringify({
      id: currentDeviceNumber,
      command: "check"
    });

    // console.log("MQTT WSS: Sending check command to get device status");
    // console.log("MQTT WSS topic:", topic);
    // console.log("MQTT WSS message:", message);

    // Send the message via MQTT WSS
    mqttService.publish(topic, message);
  };

  // Add a function to send the logs command
  const sendLogsCommand = async () => {
    setLoading(prev => ({ ...prev, logs: true }));

    try {
      // Check if MQTT WSS is connected
      if (!mqttService.isConnected) {
        // Try to reconnect if not connected
        const connected = connectToMqttBroker();

        // Wait a bit for connection to establish
        if (connected) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Check again if connected
        if (!mqttService.isConnected) {
          enqueueSnackbar(t('device_config.mqtt_connection_failed'), { variant: 'error' });
          setLoading(prev => ({ ...prev, logs: false }));
          return;
        }
      }

      // Subscribe to logs topic before sending command
      const logsTopic = `${currentDeviceNumber}/logs`;
      console.log('Subscribing to logs topic:', logsTopic);
      mqttService.subscribe(logsTopic);

      // Prepare the MQTT message
      const topic = currentDeviceNumber;
      const message = JSON.stringify({
        id: currentDeviceNumber,
        command: "get_logs"
      });

      console.log("MQTT WSS: Sending logs command");
      console.log("MQTT WSS topic:", topic);
      console.log("MQTT WSS message:", message);

      // Send the message via MQTT WSS
      const success = mqttService.publish(topic, message);

      if (success) {
        enqueueSnackbar('Logs request sent', { variant: 'success' });

        // Set timeout to unsubscribe if no response received within 30 seconds
        const timeoutId = setTimeout(() => {
          console.log('Logs request timeout, unsubscribing');
          mqttService.unsubscribe(logsTopic);
          setLoading(prev => ({ ...prev, logs: false }));
          enqueueSnackbar('Logs request timeout', { variant: 'warning' });
          setLogsTimeoutId(null);
        }, 30000);

        setLogsTimeoutId(timeoutId);

      } else {
        console.error("MQTT WSS publish failed");
        enqueueSnackbar('Failed to send logs request', { variant: 'error' });
        setLoading(prev => ({ ...prev, logs: false }));
        // Unsubscribe if publish failed
        mqttService.unsubscribe(logsTopic);
      }
    } catch (error) {
      console.error("Error in sendLogsCommand:", error);
      enqueueSnackbar('Failed to send logs request', { variant: 'error' });
      setLoading(prev => ({ ...prev, logs: false }));
    }
  };



  return (
    <Page title={t('device_config.title')}>
      <Layout />
      <Container
        maxWidth="xs"
        disableGutters
        sx={{
          px: { xs: 1, sm: 2 },
          py: { xs: 8, sm: 10 },
          overflowY: 'auto'
        }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Stack justifyContent={'space-between'} direction="row" alignItems={'center'}>
              <Typography variant="h4">{t('device_config.title')}</Typography>
              <IconButtonAnimate onClick={() => window.history.back()}>
                <Iconify icon={'ep:back'} />
              </IconButtonAnimate>
            </Stack>

            <Divider sx={{ mb: 4, mt: 1 }} />

            <Stack spacing={3} sx={{ mb: 4 }}>
              <Typography variant="subtitle1">
                {t('device_config.device_number')}: {maskDeviceNumber(currentDeviceNumber)}
              </Typography>

              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
                sx={{ mb: 2 }}>
                <Typography variant="body2">
                  {t('device_config.mqtt_broker')}: {maskBrokerAddress(brokerAddress) || t('words.not_available')}
                </Typography>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: mqttConnected ? 'success.main' : 'error.main'
                }}>
                  <Iconify
                    icon={mqttConnected ? "mdi:check-circle" : "mdi:alert-circle"}
                    width={20}
                    height={20}
                    sx={{ mr: 0.5 }}
                  />
                  <Typography variant="body2">
                    {mqttConnected
                      ? t('device_config.mqtt_connected')
                      : t('device_config.mqtt_connection_failed')}
                  </Typography>
                </Box>
                <LoadingButton
                  variant="outlined"
                  size="small"
                  onClick={connectToMqttBroker}
                  startIcon={<Iconify icon="mdi:connection" />}
                  sx={{ ml: 1 }}
                >
                  Retry
                </LoadingButton>
              </Stack>



              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
                sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ flexGrow: 1 }}>
                  {t('device_config.firmware_version')}: {deviceVersion || t('words.not_available')}
                </Typography>
                <IconButtonAnimate
                  onClick={sendCheckCommand}
                  sx={{ mr: 1 }}
                  title={t('device_config.refresh_status')}
                >
                  <Iconify icon="mdi:refresh" />
                </IconButtonAnimate>
                <LoadingButton
                  variant="contained"
                  loading={loading.update && !updateProgress.isActive}
                  onClick={sendUpdateCommand}
                  startIcon={!updateProgress.isActive ? <Iconify icon="mdi:update" /> : null}
                  sx={{
                    bgcolor: 'grey.50016',
                    border: '1px solid',
                    borderColor: 'grey.50048',
                    position: 'relative',
                    overflow: 'hidden',
                    minWidth: '200px', // Ensure consistent width
                    '&::before': updateProgress.isActive ? {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      height: '100%',
                      width: `${updateProgress.progress}%`,
                      bgcolor: updateProgress.stage === 'failed' ? 'error.main' :
                               updateProgress.stage === 'completed' ? 'success.main' :
                               updateProgress.stage === 'latest' ? 'info.main' : 'primary.main',
                      transition: 'width 0.3s ease-in-out',
                      opacity: 0.3,
                      zIndex: 0
                    } : {},
                    '& .MuiLoadingButton-loadingIndicator': {
                      display: updateProgress.isActive ? 'none' : 'flex', // Hide spinner when showing progress
                      zIndex: 2
                    },
                    '& .MuiButton-startIcon': {
                      display: updateProgress.isActive ? 'none' : 'inline-flex' // Hide icon when showing progress
                    },
                    '& > span': {
                      zIndex: 1,
                      position: 'relative',
                      color: updateProgress.isActive ? 'white' : 'inherit', // Ensure text is visible
                      fontWeight: updateProgress.isActive ? 'bold' : 'normal'
                    }
                  }}
                >
                  {updateProgress.isActive ? updateProgress.message : t('device_config.update_firmware')}
                </LoadingButton>
                <LoadingButton
                  variant="contained"
                  loading={loading.restart}
                  onClick={sendRestartCommand}
                  startIcon={<Iconify icon="mdi:restart" />}
                  sx={{ bgcolor: 'warning.main', border: '1px solid', borderColor: 'warning.dark', color: 'white' }}
                >
                  {t('device_config.restart_device')}
                </LoadingButton>
              </Stack>



              {/* Progress is now shown directly on the button */}

              {/* Sound configuration - Controls device beep sounds */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControlLabel
                  control={<Switch checked={soundEnabled} onChange={(e) => setSoundEnabled(e.target.checked)} />}
                  label={t('device_config.sound_enabled')}
                  sx={{ flexGrow: 1 }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  {t('device_config.sound_help')}
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.sound}
                  onClick={() => saveConfiguration('sound', soundEnabled)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Key configuration - Controls key detection functionality */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControlLabel
                  control={<Switch checked={keyEnabled} onChange={(e) => setKeyEnabled(e.target.checked)} />}
                  label={t('device_config.key_enabled')}
                  sx={{ flexGrow: 1 }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  {t('device_config.key_help')}
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.key}
                  onClick={() => saveConfiguration('key', keyEnabled)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Server configuration - Selects which server to connect to */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControl sx={{ flexGrow: 1 }}>
                  <InputLabel id="server-select-label">{t('device_config.server')}</InputLabel>
                  <Select
                    value={serverIndex}
                    label={t('device_config.server')}
                    labelId="server-select-label"
                    onChange={(e) => setServerIndex(e.target.value)}
                  >
                    <MenuItem value={1}>Server 1</MenuItem>
                    <MenuItem value={2}>Server 2</MenuItem>
                    <MenuItem value={3}>Server 3</MenuItem>
                    <MenuItem value={4}>Server 4</MenuItem>
                  </Select>
                  <FormHelperText>{t('device_config.server_help')}</FormHelperText>
                </FormControl>
                <LoadingButton
                  variant="contained"
                  loading={loading.server}
                  onClick={() => saveConfiguration('server', serverIndex)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Protocol configuration - Switches between XMPP and LwM2M protocols */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControl sx={{ flexGrow: 1 }}>
                  <InputLabel id="protocol-select-label">{t('device_config.protocol')}</InputLabel>
                  <Select
                    value={protocol}
                    label={t('device_config.protocol')}
                    labelId="protocol-select-label"
                    onChange={(e) => setProtocol(e.target.value)}
                  >
                    <MenuItem value="http">XMPP</MenuItem>
                    <MenuItem value="tcp">LwM2M</MenuItem>
                  </Select>
                  <FormHelperText>{t('device_config.protocol_help')}</FormHelperText>
                </FormControl>
                <LoadingButton
                  variant="contained"
                  loading={loading.protocol}
                  onClick={() => saveConfiguration('protocol', protocol)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Auto-shutdown timer - Sets time before device automatically powers off */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <TextField
                  fullWidth
                  type="number"
                  label="Асаах коммандын дараа машин автоматаар унтрах хугацаа"
                  value={autoShutdownTimer}
                  onChange={(e) => {
                    // Handle empty string case properly
                    const inputValue = e.target.value;
                    if (inputValue === '') {
                      setAutoShutdownTimer('');  // Allow empty string in the state
                    } else {
                      const numValue = parseInt(inputValue, 10);
                      setAutoShutdownTimer(Math.max(0, Math.min(120, isNaN(numValue) ? 0 : numValue)));
                    }
                  }}
                  onBlur={() => {
                    // When field loses focus, ensure we have a valid number (convert empty to 0)
                    if (autoShutdownTimer === '') {
                      setAutoShutdownTimer(0);
                    }
                  }}
                  InputProps={{
                    inputProps: {
                      min: 0,
                      max: 120,
                      step: 1
                    }
                  }}
                  helperText="Төхөөрөмж автоматаар унтрах хугацаа (0-120 минут)"
                  sx={{ flexGrow: 1 }}
                />
                <LoadingButton
                  variant="contained"
                  loading={loading.shutdown}
                  onClick={() => {
                    // Ensure we're sending a number value (0 if empty)
                    const timerValue = autoShutdownTimer === '' ? 0 : autoShutdownTimer;
                    saveConfiguration('shutdown', timerValue);
                  }}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Auto-shutdown feature - Enables/disables auto-shutdown functionality */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControlLabel
                  control={<Switch checked={autoShutdownEnabled} onChange={(e) => setAutoShutdownEnabled(e.target.checked)} />}
                  label={t('device_config.auto_shutdown_feature')}
                  sx={{ flexGrow: 1 }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  {t('device_config.auto_shutdown_feature_help')}
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.auto_shutdown}
                  onClick={() => saveConfiguration('auto_shutdown', autoShutdownEnabled)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Log level - Sets the verbosity of device logging */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControl sx={{ flexGrow: 1 }}>
                  <InputLabel id="log-level-select-label">{t('device_config.log_level')}</InputLabel>
                  <Select
                    value={logLevel}
                    label={t('device_config.log_level')}
                    labelId="log-level-select-label"
                    onChange={(e) => setLogLevel(e.target.value)}
                  >
                    <MenuItem value="debug">Debug</MenuItem>
                    <MenuItem value="info">Info</MenuItem>
                    <MenuItem value="warn">Warning</MenuItem>
                    <MenuItem value="error">Error</MenuItem>
                  </Select>
                  <FormHelperText>{t('device_config.log_level_help')}</FormHelperText>
                </FormControl>
                <LoadingButton
                  variant="contained"
                  loading={loading.log_level}
                  onClick={() => saveConfiguration('log_level', logLevel)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Geely Atlas mode - Special key control mode for Geely Atlas vehicles */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControlLabel
                  control={<Switch checked={geelyAtlasMode} onChange={(e) => setGeelyAtlasMode(e.target.checked)} />}
                  label={t('device_config.geely_atlas_mode')}
                  sx={{ flexGrow: 1 }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  {t('device_config.geely_atlas_help')}
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.geely_atlas}
                  onClick={() => saveConfiguration('geely_atlas', geelyAtlasMode)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Voltage offset - Adjusts the reported battery voltage */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <TextField
                  fullWidth
                  type="number"
                  label="Батарейн хүчдэл дээр нэмэгдэж ирж байх хүчдэлийн утга тохируулах (0-2V)"
                  value={voltageOffset}
                  onChange={(e) => {
                    // Parse the input value and ensure it's within the valid range (0-2)
                    const inputValue = e.target.value;
                    if (inputValue === '') {
                      setVoltageOffset('');  // Allow empty string in the state
                    } else {
                      const numValue = parseFloat(inputValue);
                      setVoltageOffset(Math.max(0, Math.min(2, isNaN(numValue) ? 0 : numValue)));
                    }
                  }}
                  onBlur={() => {
                    // When field loses focus, ensure we have a valid number (convert empty to 0)
                    if (voltageOffset === '') {
                      setVoltageOffset(0);
                    }
                  }}
                  InputProps={{
                    inputProps: {
                      min: 0,
                      max: 2,
                      step: 0.1
                    }
                  }}
                  helperText="Жишээ: ассан үед 13 ирж байхад 1 гэж тохируулсан үед 14 ирнэ"
                  sx={{ flexGrow: 1 }}
                />
                <LoadingButton
                  variant="contained"
                  loading={loading.voltage_offset}
                  onClick={() => {
                    // Ensure we're sending a number value (0 if empty)
                    const offsetValue = voltageOffset === '' ? 0 : voltageOffset;
                    saveConfiguration('voltage_offset', offsetValue);
                  }}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Notifications - Enables/disables voltage change notifications */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <FormControlLabel
                  control={<Switch checked={notificationsEnabled} onChange={(e) => setNotificationsEnabled(e.target.checked)} />}
                  label={t('device_config.notifications_enabled')}
                  sx={{ flexGrow: 1 }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  {t('device_config.notifications_help')}
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.notifications}
                  onClick={() => saveConfiguration('notifications', notificationsEnabled)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Voltage threshold - Sets the threshold for voltage change notifications */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={{ xs: 1.5, sm: 2 }}
                alignItems="stretch"
              >
                <TextField
                  fullWidth
                  type="number"
                  label={t('device_config.voltage_threshold')}
                  value={voltageThreshold}
                  onChange={(e) => {
                    // Parse the input value and ensure it's within a reasonable range (0-2)
                    const inputValue = e.target.value;
                    if (inputValue === '') {
                      setVoltageThreshold('');  // Allow empty string in the state
                    } else {
                      const numValue = parseFloat(inputValue);
                      setVoltageThreshold(Math.max(0, Math.min(2, isNaN(numValue) ? 0 : numValue)));
                    }
                  }}
                  onBlur={() => {
                    // When field loses focus, ensure we have a valid number (convert empty to default)
                    if (voltageThreshold === '') {
                      setVoltageThreshold(0.6);
                    }
                  }}
                  InputProps={{
                    inputProps: {
                      min: 0,
                      max: 2,
                      step: 0.1
                    }
                  }}
                  helperText={t('device_config.voltage_threshold_help')}
                  sx={{ flexGrow: 1 }}
                />
                <LoadingButton
                  variant="contained"
                  loading={loading.voltage_threshold}
                  onClick={() => {
                    // Ensure we're sending a number value (default if empty)
                    const thresholdValue = voltageThreshold === '' ? 0.6 : voltageThreshold;
                    saveConfiguration('voltage_threshold', thresholdValue);
                  }}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* GPS functionality - Enables/disables GPS tracking */}
              <Stack direction="row" spacing={2} alignItems="center">
                <FormControlLabel
                  control={<Switch checked={gpsEnabled} onChange={(e) => setGpsEnabled(e.target.checked)} />}
                  label={t('device_config.gps_enabled')}
                  sx={{ flexGrow: 1 }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  {t('device_config.gps_help')}
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.gps}
                  onClick={() => saveConfiguration('gps', gpsEnabled)}
                  sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                >
                  {t('words.save')}
                </LoadingButton>
              </Stack>

              {/* Transfer Units - Send units to device */}
              <Stack spacing={2}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Нэгж шилжүүлэх
                </Typography>
                <Stack
                  direction={{ xs: 'column', sm: 'row' }}
                  spacing={{ xs: 1.5, sm: 2 }}
                  alignItems="stretch"
                >
                  <TextField
                    fullWidth
                    type="text"
                    label="Утасны дугаар"
                    value={transferPhoneNumber}
                    onChange={(e) => {
                      // Only allow digits and ensure exactly 8 digits
                      const inputValue = e.target.value.replace(/\D/g, ''); // Remove non-digits
                      if (inputValue.length <= 8) {
                        setTransferPhoneNumber(inputValue);
                      }
                    }}
                    InputProps={{
                      inputProps: {
                        maxLength: 8,
                        pattern: '[0-9]{8}'
                      }
                    }}
                    helperText="8 оронтой Unitel утасны дугаар оруулна уу"
                    error={transferPhoneNumber.length > 0 && transferPhoneNumber.length !== 8}
                    sx={{ flexGrow: 1 }}
                  />
                  <TextField
                    fullWidth
                    type="number"
                    label="Нэгжийн тоо"
                    value={transferUnits}
                    onChange={(e) => {
                      // Parse the input value and ensure it's within the valid range (0-2000)
                      const inputValue = e.target.value;
                      if (inputValue === '') {
                        setTransferUnits('');  // Allow empty string in the state
                      } else {
                        const numValue = parseInt(inputValue, 10);
                        setTransferUnits(Math.max(0, Math.min(2000, isNaN(numValue) ? 0 : numValue)));
                      }
                    }}
                    onBlur={() => {
                      // When field loses focus, ensure we have a valid number (convert empty to 0)
                      if (transferUnits === '') {
                        setTransferUnits(0);
                      }
                    }}
                    InputProps={{
                      inputProps: {
                        min: 0,
                        max: 2000,
                        step: 1
                      }
                    }}
                    helperText="Шилжүүлэх нэгжийн тоо (хамгийн ихдээ 2000)"
                    sx={{ flexGrow: 1 }}
                  />
                </Stack>
                <Stack direction="row" justifyContent="flex-end">
                  <LoadingButton
                    variant="contained"
                    loading={loading.transfer}
                    onClick={() => {
                      // Validate phone number (exactly 8 digits)
                      if (transferPhoneNumber.length !== 8) {
                        enqueueSnackbar('Утасны дугаар яг 8 орон байх ёстой', { variant: 'warning' });
                        return;
                      }

                      // Validate units (1-2000)
                      const unitsValue = transferUnits === '' ? 0 : transferUnits;
                      if (unitsValue <= 0 || unitsValue > 2000) {
                        enqueueSnackbar('Зөв тоо оруулна уу (1-2000)', { variant: 'warning' });
                        return;
                      }

                      // Send the transfer command with both phone number and units
                      saveConfiguration('transfer', {
                        phoneNumber: transferPhoneNumber,
                        units: unitsValue
                      });
                    }}
                    startIcon={<Iconify icon="mdi:transfer" />}
                    sx={{
                      bgcolor: 'primary.main',
                      border: '1px solid',
                      borderColor: 'primary.dark',
                      color: 'white',
                      '&:hover': {
                        bgcolor: 'primary.dark'
                      }
                    }}
                  >
                    Шилжүүлэх
                  </LoadingButton>
                </Stack>
              </Stack>

              {/* Charged - License activation command */}
              <Stack direction="row" spacing={2} alignItems="center" justifyContent="center">
                <Typography variant="body2" sx={{ flexGrow: 1 }}>
                  License Activation
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1 }}>
                  Activate device license features
                </Typography>
                <LoadingButton
                  variant="contained"
                  loading={loading.charged}
                  onClick={() => saveConfiguration('charged', true)}
                  startIcon={<Iconify icon="mdi:license" />}
                  sx={{
                    bgcolor: 'success.main',
                    border: '1px solid',
                    borderColor: 'success.dark',
                    color: 'white',
                    '&:hover': {
                      bgcolor: 'success.dark'
                    }
                  }}
                >
                  Activate License
                </LoadingButton>
              </Stack>

              <Divider sx={{ my: 3 }} />

              {/* Get Logs - Rarely used diagnostic function */}
              <Stack direction="row" spacing={2} alignItems="center" justifyContent="center">
                <LoadingButton
                  variant="outlined"
                  loading={loading.logs}
                  onClick={sendLogsCommand}
                  startIcon={<Iconify icon="mdi:file-document-outline" />}
                  sx={{
                    borderColor: 'info.main',
                    color: 'info.main',
                    '&:hover': {
                      bgcolor: 'info.main',
                      color: 'white'
                    }
                  }}
                >
                  Get Device Logs
                </LoadingButton>
              </Stack>
            </Stack>
          </Grid>
        </Grid>
      </Container>

      {/* Logs Dialog */}
      <Dialog
        open={logsDialogOpen}
        onClose={() => setLogsDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { maxHeight: '80vh' }
        }}
      >
        <DialogTitle>
          Device Logs - {maskDeviceNumber(currentDeviceNumber)}
        </DialogTitle>
        <DialogContent>
          <Box
            component="pre"
            sx={{
              backgroundColor: 'grey.100',
              padding: 2,
              borderRadius: 1,
              fontSize: '0.875rem',
              fontFamily: 'monospace',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              maxHeight: '60vh',
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'grey.300'
            }}
          >
            {deviceLogs || 'No logs available'}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              navigator.clipboard.writeText(deviceLogs);
              enqueueSnackbar('Logs copied to clipboard', { variant: 'success' });
            }}
            startIcon={<Iconify icon="mdi:content-copy" />}
          >
            Copy
          </Button>
          <Button onClick={() => setLogsDialogOpen(false)} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Page>
  );
}
