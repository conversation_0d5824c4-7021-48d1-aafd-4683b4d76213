import { Link as RouterLink } from 'react-router-dom';
// @mui
import { styled } from '@mui/material/styles';
import { <PERSON>, Button, Link, Container, Typography } from '@mui/material';
// layouts
import LogoOnlyLayout from '../../layout/LogoOnlyLayout';
// components
import Page from '../../components/Page';
import Iconify from '../../components/Iconify';
import CarFront from '../../components/CarFront'
// sections
import  VerifyCodeForm  from '../../sections/auth/VerifyCodeForm';
// import Timer from '../../components/Timer';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(8, 0),
  height:'100vh'
}));

// ----------------------------------------------------------------------

export default function VerifyCode() { 
  return (
    <Page title="Verify" sx={{ height: 1 }}>
      <RootStyle>
        <LogoOnlyLayout />

        <Container>
          <Box sx={{ maxWidth: 480, mx: 'auto', textAlign: 'center' }}>
            <Button
              size="small"
              component={RouterLink}
              to={"/auth/login"}
              startIcon={<Iconify icon={'eva:arrow-ios-back-fill'} width={20} height={20} />}
              sx={{color:'text.primary' }}
            >
              Back
            </Button>
            <Box width={"50%"} sx={{ mx: 'auto', mb: 3}}>
             <CarFront/>
            </Box>
            <Typography variant="h3" paragraph >
              Verification Code
            </Typography>
            <Typography  paragraph>
              Please enter cerification code we sent to your phone number
            </Typography>
            <Box sx={{ mt: 5, mb: 3 }}>
              <VerifyCodeForm />
            </Box>
            <Typography variant="body2">
              Haven't got your OTP number yet?  &nbsp;
              <Link variant="subtitle2" underline="none" sx={{cursor:'pointer',color:'text.secondary'}} onClick={() => { }}>
                Resend code
              </Link>
            </Typography>
          </Box>
          <div id = 'recaptcha-container' />
        </Container>
      </RootStyle>
    </Page>
  );
}
