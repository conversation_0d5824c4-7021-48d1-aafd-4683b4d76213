// @mui
import { styled } from "@mui/material/styles";
import { Box, Typography } from "@mui/material";

// components
import Page from "../../components/Page";

// Lazy load CarFront and ForgotPasswordForm components
import { lazy } from "react";

const CarFront = lazy(() => import("../../components/CarFront"));
const ForgotPasswordForm = lazy(() => import("../../sections/auth/ForgotPasswordForm"));

// ----------------------------------------------------------------------

const ContentStyle = styled('div')(({ theme }) => ({
  maxWidth: 480,
  margin: 'auto',
  display: 'flex',
  minHeight: '100vh',
  flexDirection: 'column',
  justifyContent: 'center',
  alignContent: 'space-between',
  gap: 3
}));

// ----------------------------------------------------------------------

export default function ForgotPassword() {
  return (
    <Page title="Forgot Password">
      <ContentStyle>
        <Typography variant="h3" gutterBottom textAlign={'center'}>
         Шинэ нууц үг үүсгэх
         </Typography>

        <Box width={"50%"} sx={{ mx: 'auto', mb: 3, mt: -3 }}>
          <CarFront />
        </Box>

        {/* Updated Instruction Text */}
        <Typography paragraph textAlign={'center'}>
        Таны нууц үгээ сэргээхийн тулд төхөөрөмжийн дугаарын сүүлийн 6 оронг оруулна уу. Хэрэв та төхөөрөмжийн дугаарыг мэдэхгүй бол та машинруугаа мессежээр id гэж бичээд илгээж авна уу. Жич хариу авахад машинд нэгж хоног байхыг анхаарна уу.        </Typography>

        <Box width={'80%'} mx={'auto'} my={3}>
          <ForgotPasswordForm /> {/* Ensure this form accepts the new logic for resetting */}
        </Box>
      </ContentStyle>
    </Page>
  );
}
