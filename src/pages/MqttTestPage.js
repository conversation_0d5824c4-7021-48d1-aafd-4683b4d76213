import React from 'react';
import { Container, Typography, Box, Tabs, Tab, Alert } from '@mui/material';
import Page from '../components/Page';
import MqttTest from '../components/MqttTest';
import SimpleMqttTest from '../components/SimpleMqttTest';
import HttpMqttTest from '../components/HttpMqttTest';
import DirectMqttTest from '../components/DirectMqttTest';

const MqttTestPage = () => {
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Page title="MQTT Test">
      <Container maxWidth="lg">
        <Box sx={{ mb: 5 }}>
          <Typography variant="h4" gutterBottom>
            MQTT Connection Test
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Testing connection with the default EMQX broker (broker.emqx.io)
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          If WebSocket connections don't work, try the HTTP API test which uses a different approach.
        </Alert>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Simple WebSocket Test" />
            <Tab label="Direct URL Test" />
            <Tab label="Advanced WebSocket Test" />
            <Tab label="HTTP API Test" />
          </Tabs>
        </Box>

        {tabValue === 0 && <SimpleMqttTest />}
        {tabValue === 1 && <DirectMqttTest />}
        {tabValue === 2 && <MqttTest />}
        {tabValue === 3 && <HttpMqttTest />}
      </Container>
    </Page>
  );
};

export default MqttTestPage;
