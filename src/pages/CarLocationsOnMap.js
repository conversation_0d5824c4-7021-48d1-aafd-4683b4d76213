import { <PERSON><PERSON>, Con<PERSON>er, Menu<PERSON>tem, Select, <PERSON>ack, TextField, Typography } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import { Icon } from "@iconify/react";
import * as L from "leaflet";
import 'leaflet/dist/leaflet.css';
import "leaflet-routing-machine";

import { useState } from "react";
import Page from "../components/Page";
import Layout from "../layout";
import axios from "../utils/axios";
import { IconButtonAnimate } from "../components/animate";
import { useEffect } from "react";
import { formatSQLDate } from "../utils/formatUtils";
import useAuth from "../hooks/useAuth";

function createIcon(url) {
    return new L.Icon({
        iconUrl: url,
        iconSize: [40, 40]
    });
}
let virtualTime = 0, playSpeed = 1, logIndex = 0;
let interval = 0;
export default function CarLocationsOnMap() {
    const [playIcon, setPlayIcon] = useState('carbon:play-outline');
    const [carLocation, setCarLocation] = useState({ lat: 47.8994, lng: 106.82951 });
    const [from, setFrom] = useState(formatSQLDate(new Date(new Date().getTime() - 24 * 15 * 60 * 60 * 1000)));
    const [to, setTo] = useState(formatSQLDate(new Date()));
    const [lat, setLat] = useState(47.8994);
    const [lng, setLng] = useState(106.8994);
    const [log, setLog] = useState({});
    const [playTime, setPlayTime] = useState(new Date().toISOString());
    const [speed, setSpeed] = useState(1);
    const { user } = useAuth();
    const [deviceNumber, setDeviceNumber] = useState(user?.device?.deviceNumber);
    const getMarkerIcon = (index) => {
        if (index === 0)
            return createIcon("/images/car-map-icon.png");
        return createIcon("/images/car-map-icon.png");
    }
    const forward = () => {
        if (speed < 10) {
            playSpeed = speed + 1;
            setSpeed(playSpeed);
        }


    }
    const backword = () => {
        if (speed >= 0.2) {
            playSpeed = speed - 1;
            setSpeed(playSpeed);
        }

    }
    const MapHook = ({ location }) => {
        const map = useMap();
        if (location.lat && location.lng)
            map.setView([location.lat, location.lng], 13)
        return null;
    }
    const stop = () => {
        clearInterval(interval);
        interval = 0;
    }
    const play = () => {
        if (playTime <= 0)
            return;
        if (interval > 0) {
            stop();
            setPlayIcon('carbon:play-outline');
        }
        else {
            setPlayIcon('bx:stop-circle');
            interval = setInterval(() => {
                if (logIndex >= log.length - 1) {
                    logIndex = 0;
                    stop();
                    setPlayIcon('carbon:play-outline');
                }

                else

                    // virtualTime += playSpeed * 100;
                    // for (let i = 0; i < log.length; i++) {
                    //     if (i !== log.length - 1 && new Date(log[i].receiveTime).getTime() <= virtualTime && new Date(log[i + 1].receiveTime).getTime() > virtualTime) {
                    try {
                        let data = {};
                        if (log[logIndex].message?.includes('Lat')) {
                            data = JSON.parse(log[logIndex].message);
                        }
                        if (log[logIndex].response?.includes('Lon')) {
                            data = JSON.parse(log[logIndex].response);
                        }
                        if (data.Lat && data.Lon) {
                            setCarLocation({ lat: data.Lat, lng: data.Lon });

                            setLat(data.Lat);
                            setLng(data.Lon);

                        }
                        // setLog(log.splice(0, i + 1));
                        // setLog(log[logIndex]);
                        setPlayTime(new Date(log[logIndex].receiveTime));
                        //     break;
                        logIndex++;
                    }
                    catch (err) {
                        console.log(err)
                    }
                // }
                // }


            }, 2000 / playSpeed)
        }

    }

    useEffect(() => {
        if (deviceNumber !== '')
            load();
    }, [deviceNumber])
    const load = () => {
        axios.post('/api/log/get-by-date', { from, to, deviceNumber }).then(
            (res) => {
                // console.log(res.data);
                if (res.status === 200) {
                    setLog(res.data.logs);
                    if (res.data.logs.length === 0) {
                        setPlayTime(0);
                    }
                    else {
                        //  console.log(res.data.logs[0]?.receiveTime)
                        const time = new Date(res.data.logs[0]?.receiveTime).getTime() || new Date().getTime();
                        virtualTime = time;
                        setPlayTime(time);

                    }

                }
            }
        );
    }
    useEffect(() => {
        //  console.log(user)
        return () => {
            clearInterval(interval);
        };
    }, []);
    return (
        <Page title="Device Profile">
            <Container sx={{ py: { xs: 10 } }}>
                <Layout />

                <Stack >
                    <Stack direction="row" justifyContent={'center'} alignItems={'center'} gap={1}>
                        <Typography>
                            Select Device
                        </Typography>
                        <Select value={deviceNumber} onChange={(e) => (setDeviceNumber(e.target.value))} labelId="type-select-label">
                            {user?.devices?.map((dn, index) => (
                                <MenuItem value={dn.deviceNumber} key={index}>{dn.type} - {dn.deviceNumber}</MenuItem>
                            ))}

                        </Select></Stack>
                    <Stack direction="row" justifyContent={'center'} alignItems={'center'} gap={1} py={1}>
                        <TextField size="small" defaultValue={from} onChange={(e) => { setFrom(e.target.value) }}></TextField>
                        <Typography>-</Typography>
                        <TextField size="small" defaultValue={to} onChange={(e) => { setTo(e.target.value) }}></TextField>


                        <IconButtonAnimate onClick={() => load()}
                            sx={
                                {
                                    p: 0,

                                }
                            } >
                            <Icon icon={'ei:refresh'} width={30}></Icon>
                        </IconButtonAnimate>
                    </Stack>
                    <Stack>
                        <MapContainer center={[lat, lng]} zoom={13} style={{ height: "60vh" }}>
                            <TileLayer
                                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                                attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
                            />
                            <Marker position={{ lat: carLocation.lat, lng: carLocation.lng }} icon={getMarkerIcon(0)} />
                            <MapHook location={carLocation} />
                        </MapContainer>
                    </Stack>
                    <Stack direction='row' justifyContent={'center'} p={2} gap={2} alignItems={'center'}>
                        <Typography variant = "subtitle1">{speed}X</Typography>
                        <IconButtonAnimate onClick={() => backword()}
                            sx={
                                {
                                    p: 0,

                                }
                            } >
                            <Icon icon={'icomoon-free:backward'} width={28}></Icon>
                        </IconButtonAnimate>
                        <IconButtonAnimate onClick={() => play()}
                            sx={
                                {
                                    p: 0,

                                }
                            } >
                            <Icon icon={playIcon} width={30}></Icon>

                        </IconButtonAnimate>
                        <IconButtonAnimate onClick={() => forward()}
                            sx={
                                {
                                    p: 0,

                                }
                            } >
                            <Icon icon={'bx:fast-forward-circle'} width={30}></Icon>
                        </IconButtonAnimate>
                        <Typography color={'white'} sx={{ width: '300px' }}>
                            {playTime > 0 ? new Date(playTime).toISOString() : 'No data'}
                        </Typography>
                    </Stack>
                </Stack>
            </Container>
        </Page >
    )
}
