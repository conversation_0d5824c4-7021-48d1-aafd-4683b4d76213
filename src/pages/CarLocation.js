import PropTypes from 'prop-types';
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON>ayer } from 'react-leaflet';
import * as L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// ----------------------------------------------------------------------

CarLocation.propTypes = {
  lat: PropTypes.number,
  lng: PropTypes.number,
  center: PropTypes.object,
};

function createIcon(url) {
  return new L.Icon({
    iconUrl: url,
    iconSize: [40, 40],
  });
}

export default function CarLocation({ center, lat, lng }) {
  const getMarkerIcon = (index) => {
    if (index === 0) {
      return createIcon('/images/car-map-icon.png');
    }
    return createIcon('/images/car-map-icon.png');
  };

  const defaultProps = {
    center: {
      lat: center?.lat || 10.99835602,
      lng: center?.lng || 77.01502627,
    },
    zoom: 13,
  };

  return (
    <MapContainer
      center={{ lat, lng }}
      zoom={defaultProps.zoom}
      style={{ height: '100vh' }}
    >
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution="&copy; OpenStreetMap contributors"
      />
      <Marker position={{ lat, lng }} icon={getMarkerIcon(0)} />
    </MapContainer>
  );
}
