import { useState, useEffect } from 'react';
// @mui
import {
    Card,
    Table,
  
    Checkbox,
    TableRow,
    TableBody,
    TableCell,
    Container,
    TableContainer,
    TablePagination,
    Divider,
    Skeleton
} from '@mui/material';
// components
import Page from '../components/Page';
import Scrollbar from '../components/Scrollbar';
import SearchNotFound from '../components/SearchNotFound';
import { getYMDHISDate } from '../utils/formatUtils';
import EbarimtDialog from './EbarimtDialog';
// sections
import { LogListHead, LogListToolbar, InvoiceLogMoreMenu } from "../sections/log";

import axios from "../utils/axios";
import Layout from '../layout';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
    { id: 'invoice', label: 'invoice_log.invoice_number' },
    { id: 'licenseKey', label: 'invoice_log.license_key' },
    { id: 'cost', label: 'invoice_log.cost' },
    { id: 'createdAt', label: 'invoice_log.created' },
    { id: 'expired', label: 'invoice_log.expired' },
    { id: 'realInvoice', label: 'invoice_log.real_invoice_id' },
    { id: '' },
];

// ----------------------------------------------------------------------


export default function InvoiceLog() {
    const [loading, setLoading] = useState(false);
    const [logList, setLogList] = useState([]);
    const [page, setPage] = useState(0);
    const [order, setOrder] = useState('desc');
    const [selected, setSelected] = useState([]);
    const [orderBy, setOrderBy] = useState('createdAt');
    const [filterName, setFilterName] = useState('');
    const [rowsPerPage, setRowsPerPage] = useState(10);

    const handleRequestSort = (property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };

    const handleSelectAllClick = (event) => {
        if (event.target.checked) {
            const newSelecteds = filteredLogs.splice(0, Math.min(rowsPerPage, logList.length)).map((n) => n._id);
            setSelected(newSelecteds);
            return;
        }
        setSelected([]);
    };

    const handleClick = (_id) => {
        const selectedIndex = selected.indexOf(_id);
        let newSelected = [];
        if (selectedIndex === -1) {
            newSelected = newSelected.concat(selected, _id);
        } else if (selectedIndex === 0) {
            newSelected = newSelected.concat(selected.slice(1));
        } else if (selectedIndex === selected.length - 1) {
            newSelected = newSelected.concat(selected.slice(0, -1));
        } else if (selectedIndex > 0) {
            newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
        }
        setSelected(newSelected);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleFilterByName = (filterName) => {
        setFilterName(filterName);
        setPage(0);
    };

    const handleDeleteLog = async (_id) => {
        const deleteLog = logList.filter((log) => log._id !== _id);
        const res = await axios.post('/api/license/delete', { ids: [_id] });
        if (res.status === 200 && res.data.success) {
            setSelected([]);
            setLogList(deleteLog);
        }

    };

    const handleDeleteMultiLog = async (selected) => {
        const deleteLogs = logList.filter((log) => !selected.includes(log._id));
        const res = await axios.post('/api/license/delete', { ids: selected });
        if (res.status === 200 && res.data.success) {
            setSelected([]);
            setLogList(deleteLogs);
        }
    };

    useEffect(() => {
        setLoading(true)
        axios.post('/api/license/list').then(res => {

            setLogList(res.data.data.logs);
        })
            .catch(err => {

            }).finally(() => setLoading(false))
    }, []);
    const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - logList.length) : 0;

    const filteredLogs = applySortFilter(logList, getComparator(order, orderBy), filterName);

    const isNotFound = !filteredLogs.length && Boolean(filterName);
    const handleChangeTime = (time) => {

    }
    const [viewEbarimt, setViewEbarimt] = useState(false);
    const [invoiceID, setInvoiceID] = useState("");

    const handleDetail = (realInvoice) => {
        try {
            setInvoiceID(realInvoice);
            setViewEbarimt(true);
        }
        catch (err) {
            console.log(err);
        }
    }
    return (
        <Page title="Log Management">
            <Container sx={{ py: { xs: 12 } }}>
                <Layout />
                <Card sx={{ mb: 2, p: 2 }}>
                    <LogListToolbar
                        numSelected={selected.length}
                        filterName={filterName}
                        onFilterName={handleFilterByName}
                        onDeleteLog={() => handleDeleteMultiLog(selected)}
                        onChangeTime={handleChangeTime}
                    />
                    <Divider />
                    <Scrollbar >
                        {loading &&
                            [1, 2, 3, 4, 5].map((index) => (
                                <Skeleton height={40} key={index} />
                            ))
                        }
                        {!loading &&
                            <TableContainer sx={{ minWidth: 850, maxHeight: '70vh' }} >
                                <Table size="small" stickyHeader>
                                    <LogListHead
                                        order={order}
                                        orderBy={orderBy}
                                        headLabel={TABLE_HEAD}
                                        rowCount={logList.length}
                                        numSelected={selected.length}
                                        onRequestSort={handleRequestSort}
                                        onSelectAllClick={handleSelectAllClick}
                                    />
                                    <TableBody>
                                        {filteredLogs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row) => {
                                            const { _id, invoice, licenseKey, cost, createdAt, expired, realInvoice } = row;
                                            const isItemSelected = selected.indexOf(_id) !== -1;

                                            return (
                                                <TableRow
                                                    hover
                                                    key={_id}
                                                    tabIndex={-1}
                                                    role="checkbox"
                                                    selected={isItemSelected}
                                                    aria-checked={isItemSelected}
                                                >
                                                    <TableCell padding="checkbox">
                                                        <Checkbox checked={isItemSelected} onClick={() => handleClick(_id)} />
                                                    </TableCell>
                                                    <TableCell align="left">{invoice || " "}</TableCell>
                                                    <TableCell align="left">{licenseKey || " "}</TableCell>
                                                    <TableCell align="left">{cost || " "}</TableCell>
                                                    <TableCell align="left">{getYMDHISDate(createdAt) || " "}</TableCell>
                                                    <TableCell align="left">{getYMDHISDate(expired) || " "}</TableCell>

                                                    <TableCell align="left">
                                                        {realInvoice || " "}
                                                    </TableCell>

                                                    <TableCell align="right">
                                                        <InvoiceLogMoreMenu id={_id} onDelete={() => handleDeleteLog(_id)} onDetail={() => handleDetail(realInvoice)} />
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                        {emptyRows > 0 && (
                                            <TableRow style={{ height: 53 * emptyRows }}>
                                                <TableCell colSpan={10} />
                                            </TableRow>
                                        )}
                                    </TableBody>
                                    {isNotFound && (
                                        <TableBody>
                                            <TableRow>
                                                <TableCell align="center" colSpan={10} sx={{ py: 3 }}>
                                                    <SearchNotFound searchQuery={filterName} />
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    )}
                                </Table>
                            </TableContainer>
                        }
                    </Scrollbar>
                    <TablePagination
                        rowsPerPageOptions={[5, 10, 25, 50]}
                        component="div"
                        count={filteredLogs.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={(e, page) => setPage(page)}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                </Card>

                {viewEbarimt && (
                    <EbarimtDialog open={viewEbarimt} onClose={() => setViewEbarimt(false)} invoiceID={invoiceID} />
                )}

            </Container>
        </Page>
    );
}

// ----------------------------------------------------------------------

function descendingComparator(a, b, orderBy) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}

function getComparator(order, orderBy) {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
    const stabilizedThis = array.map((el, index) => [el, index]);
    stabilizedThis.sort((a, b) => {
        const order = comparator(a[0], b[0]);
        if (order !== 0) return order;
        return a[1] - b[1];
    });
    if (query) {
        return array.filter((_Log) => (_Log.invoice.includes(query.toLowerCase()) || (_Log.licenseKey.toLowerCase().includes(query.toLowerCase()))));
    }
    return stabilizedThis.map((el) => el[0]);
}
