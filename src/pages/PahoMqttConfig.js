import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  CircularProgress,
  Alert,
  TextField,
  Card,
  CardContent,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import Page from '../components/Page';
// Import Paho MQTT client
import { Client } from 'paho-mqtt';

const PahoMqttConfig = () => {
  // Connection states
  const [client, setClient] = useState(null);
  const [status, setStatus] = useState('Disconnected');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState([]);
  const [messages, setMessages] = useState([]);
  const [publishMessage, setPublishMessage] = useState('');

  // Broker settings
  const [brokerIp, setBrokerIp] = useState('*************');
  const [brokerPort, setBrokerPort] = useState('8083');
  const [brokerPath, setBrokerPath] = useState('/mqtt');
  const brokerTopic = 'aslaa/test';

  // Client ID with random suffix
  const clientId = useRef(`paho_device_config_${Math.random().toString(16).substring(2, 10)}`);

  // Define all possible broker configurations to try in order
  const alternativeBrokers = [
    // First try the primary IP with different protocols and ports
    { ip: '*************', port: '8083', path: '/mqtt' },
    { ip: '*************', port: '8083', path: '/' },
    { ip: '*************', port: '8084', path: '/mqtt' },

    // Then try the other IPs
    { ip: '************', port: '8083', path: '/mqtt' },
    { ip: '************', port: '8084', path: '/mqtt' },

    { ip: '**************', port: '8083', path: '/mqtt' },
    { ip: '**************', port: '8084', path: '/mqtt' },

    // Public EMQX broker as last resort
    { ip: 'broker.emqx.io', port: '8083', path: '/mqtt' },
    { ip: 'broker.emqx.io', port: '8084', path: '/mqtt' }
  ];

  // Add log entry with timestamp
  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  // Add received message to messages list
  const addMessage = (topic, message) => {
    const timestamp = new Date().toLocaleTimeString();
    setMessages(prev => [
      ...prev,
      {
        id: Date.now(),
        topic,
        message,
        time: timestamp
      }
    ]);
  };

  // Connect to MQTT broker using Paho client
  const connectMqtt = (
    brokerAddress = brokerIp,
    port = brokerPort,
    path = brokerPath,
    tryAlternative = true,
    alternativeIndex = 0
  ) => {
    setIsLoading(true);
    setStatus('Connecting...');
    setError(null);

    try {
      // Disconnect existing client if any
      if (client) {
        try {
          client.disconnect();
        } catch (e) {
          console.error('Error disconnecting existing client:', e);
        }
      }

      addLog(`Connecting to MQTT broker: ${brokerAddress}:${port}${path}`);

      // Create a new Paho MQTT client
      const mqttClient = new Client(brokerAddress, Number(port), path, clientId.current);

      // Set connection timeout
      const connectionTimeout = setTimeout(() => {
        if (status !== 'Connected') {
          addLog(`Connection timeout for ${brokerAddress}:${port}${path}`);

          try {
            mqttClient.disconnect();
          } catch (e) {
            console.error('Error disconnecting client after timeout:', e);
          }

          // Try alternative broker if available
          if (tryAlternative && alternativeIndex < alternativeBrokers.length) {
            const alternative = alternativeBrokers[alternativeIndex];
            addLog(`Trying alternative broker: ${alternative.ip}:${alternative.port}${alternative.path}`);
            setBrokerIp(alternative.ip);
            setBrokerPort(alternative.port);
            setBrokerPath(alternative.path);
            connectMqtt(
              alternative.ip,
              alternative.port,
              alternative.path,
              true,
              alternativeIndex + 1
            );
          } else if (tryAlternative) {
            addLog('All brokers failed. Please check your network connection.');
            setError('Failed to connect to any broker. Please check your network connection.');
            setIsLoading(false);
            setStatus('Error');
          }
        }
      }, 15000); // 15 seconds timeout

      // Set up callbacks
      mqttClient.onConnectionLost = (responseObject) => {
        setStatus('Disconnected');
        setIsLoading(false);
        addLog(`Connection lost: ${responseObject.errorMessage}`);
        console.log('Connection lost:', responseObject);
      };

      mqttClient.onMessageArrived = (message) => {
        const topic = message.destinationName;
        const payload = message.payloadString;
        addLog(`Received message on ${topic}: ${payload}`);
        addMessage(topic, payload);
      };

      // Connect options
      const options = {
        timeout: 30,  // 30 seconds
        keepAliveInterval: 60,
        cleanSession: true,
        useSSL: port === '8084',
        onSuccess: () => {
          clearTimeout(connectionTimeout);
          setStatus('Connected');
          setClient(mqttClient);
          setIsLoading(false);
          addLog(`Connected to MQTT broker successfully at ${brokerAddress}:${port}${path}!`);

          // Subscribe to the topic automatically
          mqttClient.subscribe(brokerTopic, {
            qos: 0,
            onSuccess: () => {
              addLog(`Subscribed to ${brokerTopic}`);
            },
            onFailure: (err) => {
              addLog(`Error subscribing to ${brokerTopic}: ${err.errorMessage}`);
              setError(`Failed to subscribe: ${err.errorMessage}`);
            }
          });
        },
        onFailure: (err) => {
          clearTimeout(connectionTimeout);
          setStatus('Error');
          setError(`Connection error: ${err.errorMessage}`);
          setIsLoading(false);
          addLog(`Connection error: ${err.errorMessage}`);
          console.error('MQTT Error:', err);

          // Try alternative broker if available
          if (tryAlternative && alternativeIndex < alternativeBrokers.length) {
            const alternative = alternativeBrokers[alternativeIndex];
            addLog(`Trying alternative broker: ${alternative.ip}:${alternative.port}${alternative.path}`);
            setBrokerIp(alternative.ip);
            setBrokerPort(alternative.port);
            setBrokerPath(alternative.path);
            connectMqtt(
              alternative.ip,
              alternative.port,
              alternative.path,
              true,
              alternativeIndex + 1
            );
          }
        }
      };

      // Connect to the broker
      mqttClient.connect(options);

    } catch (err) {
      setStatus('Error');
      setError(`Exception: ${err.message}`);
      setIsLoading(false);
      addLog(`Exception: ${err.message}`);
      console.error('MQTT Connection Exception:', err);

      // Try alternative broker if available
      if (tryAlternative && alternativeIndex < alternativeBrokers.length) {
        const alternative = alternativeBrokers[alternativeIndex];
        addLog(`Trying alternative broker: ${alternative.ip}:${alternative.port}${alternative.path}`);
        setBrokerIp(alternative.ip);
        setBrokerPort(alternative.port);
        setBrokerPath(alternative.path);
        connectMqtt(
          alternative.ip,
          alternative.port,
          alternative.path,
          true,
          alternativeIndex + 1
        );
      }
    }
  };

  // Disconnect from MQTT broker
  const disconnectMqtt = () => {
    if (client) {
      try {
        client.disconnect();
        setClient(null);
        setStatus('Disconnected');
        addLog('Disconnected from MQTT broker');
      } catch (err) {
        addLog(`Error disconnecting: ${err.message}`);
        console.error('Error disconnecting:', err);
      }
    }
  };

  // Publish a message to the topic
  const publishToTopic = () => {
    if (client && publishMessage) {
      try {
        // Create a new message object
        const message = new Client.Message(publishMessage);
        message.destinationName = brokerTopic;
        client.send(message);
        addLog(`Published to ${brokerTopic}: ${publishMessage}`);
        setPublishMessage(''); // Clear the input field after publishing
      } catch (err) {
        addLog(`Error publishing: ${err.message}`);
        setError(`Failed to publish: ${err.message}`);
      }
    }
  };

  // Connect automatically when component mounts
  useEffect(() => {
    connectMqtt();

    // Clean up on component unmount
    return () => {
      if (client) {
        try {
          client.disconnect();
        } catch (e) {
          console.error('Error disconnecting on unmount:', e);
        }
      }
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Page title="MQTT Configuration (Paho)">
      <Container maxWidth="lg">
        <Box sx={{ mb: 5 }}>
          <Typography variant="h4" gutterBottom>
            MQTT Configuration (Paho Client)
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Connected to broker {brokerIp}:{brokerPort} (path: {brokerPath}) and subscribed to {brokerTopic}
          </Typography>
          {status === 'Connected' && (
            <Alert severity="success" sx={{ mt: 2 }}>
              Successfully connected to {brokerIp}:{brokerPort} (path: {brokerPath})
            </Alert>
          )}
          {status === 'Error' && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Having trouble connecting? Try the "Try Alternative Broker" button to connect to a different broker.
            </Alert>
          )}
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Connection Status
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="body1" sx={{ mr: 1 }}>
                    Status:
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: status === 'Connected' ? 'green' :
                             status === 'Connecting...' || status === 'Reconnecting' ? 'orange' :
                             'error.main',
                      fontWeight: 'bold'
                    }}
                  >
                    {status}
                  </Typography>
                  {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
                </Box>

                {error && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                  </Alert>
                )}

                <Box sx={{ mt: 2, p: 1, bgcolor: 'background.neutral', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    <strong>Active Broker:</strong> {brokerIp}:{brokerPort}
                  </Typography>
                  <Typography variant="subtitle2" gutterBottom>
                    <strong>Path:</strong> {brokerPath}
                  </Typography>
                  <Typography variant="subtitle2" gutterBottom>
                    <strong>Connection URL:</strong> ws://{brokerIp}:{brokerPort}{brokerPath}
                  </Typography>
                  <Typography variant="subtitle2" gutterBottom>
                    <strong>Topic:</strong> {brokerTopic}
                  </Typography>
                  <Typography variant="subtitle2" gutterBottom>
                    <strong>Client ID:</strong> {clientId.current}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    variant="contained"
                    onClick={() => connectMqtt()}
                    disabled={status === 'Connected' || status === 'Connecting...' || isLoading}
                  >
                    Connect
                  </Button>

                  <Button
                    variant="outlined"
                    onClick={disconnectMqtt}
                    disabled={!client || status === 'Disconnected'}
                  >
                    Disconnect
                  </Button>
                </Box>

                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="text"
                    color="secondary"
                    onClick={() => {
                      if (client) {
                        try {
                          client.disconnect();
                        } catch (e) {
                          console.error('Error disconnecting:', e);
                        }
                      }
                      // Try the first alternative broker
                      if (alternativeBrokers.length > 0) {
                        const alternative = alternativeBrokers[0];
                        addLog(`Manually trying alternative broker: ${alternative.ip}:${alternative.port}${alternative.path}`);
                        setBrokerIp(alternative.ip);
                        setBrokerPort(alternative.port);
                        setBrokerPath(alternative.path);
                        connectMqtt(
                          alternative.ip,
                          alternative.port,
                          alternative.path,
                          true,
                          1
                        );
                      }
                    }}
                    disabled={status === 'Connecting...' || isLoading}
                    size="small"
                  >
                    Try Alternative Broker
                  </Button>
                </Box>
              </CardContent>
            </Card>

            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Publish Message
                </Typography>

                <TextField
                  label="Message"
                  variant="outlined"
                  size="small"
                  fullWidth
                  multiline
                  rows={3}
                  value={publishMessage}
                  onChange={(e) => setPublishMessage(e.target.value)}
                  placeholder="Enter message to publish"
                  sx={{ mb: 2 }}
                  disabled={status !== 'Connected'}
                />

                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={publishToTopic}
                  disabled={status !== 'Connected' || !publishMessage}
                >
                  Publish to {brokerTopic}
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Received Messages
                </Typography>

                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    height: 300,
                    overflow: 'auto',
                    bgcolor: 'grey.50',
                    mb: 2
                  }}
                >
                  {messages.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" align="center">
                      No messages received yet
                    </Typography>
                  ) : (
                    <List>
                      {messages.map((msg, index) => (
                        <React.Fragment key={msg.id}>
                          {index > 0 && <Divider />}
                          <ListItem>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="subtitle2" color="primary">
                                    {msg.topic}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {msg.time}
                                  </Typography>
                                </Box>
                              }
                              secondary={
                                <Typography
                                  variant="body2"
                                  sx={{
                                    wordBreak: 'break-word',
                                    whiteSpace: 'pre-wrap'
                                  }}
                                >
                                  {msg.message}
                                </Typography>
                              }
                            />
                          </ListItem>
                        </React.Fragment>
                      ))}
                    </List>
                  )}
                </Paper>

                <Typography variant="h6" gutterBottom>
                  Connection Logs
                </Typography>

                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    height: 200,
                    overflow: 'auto',
                    bgcolor: 'grey.900',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}
                >
                  {logs.length === 0 ? (
                    <Typography variant="body2" color="text.secondary">
                      No logs yet
                    </Typography>
                  ) : (
                    logs.map((log, index) => (
                      <Typography key={index} variant="body2" color="grey.300" sx={{ mb: 0.5 }}>
                        {log}
                      </Typography>
                    ))
                  )}
                </Paper>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Page>
  );
};

export default PahoMqttConfig;
