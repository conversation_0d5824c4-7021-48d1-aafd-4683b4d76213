import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import LandingHero from '../sections/landing/LadingHero';
import LandingHeader from '../sections/landing/LandingHeader';
import WidePage from "../components/WidePage";
import LandingDownload from '../sections/landing/LandingDownload';
import LandingFeature from '../sections/landing/LandingFeature';
import LandingAbout from 'src/sections/landing/LandingAbout';
import { messaging, getToken } from 'src/config/firebase-config';  // Import getToken properly

const requestPermission = async () => {
  try {
    // Request permission to show notifications
    const permission = await Notification.requestPermission();

    if (permission === 'granted') {
      console.log('Notification permission granted.');

      // Use getToken function from Firebase Modular SDK
      const token = await getToken(messaging, {
        vapidKey: 'BDMievQt-9l21wJxGBQ-9Qamb6igxvWMnKNV-26s5Y-BV-kUoM7RJs_7DWelbZ0qU8e5P5Lct0vWQvZKr8mhYKo', // Replace with your actual VAPID key
      });

      console.log('FCM Token:', token);

      // Optionally, send the token to your server to associate with the user
    } else {
      console.log('Notification permission denied.');
    }
  } catch (error) {
    console.error('Error getting permission or token:', error);
  }
};

export default function Landing() {
  const { t } = useTranslation();

  useEffect(() => {
    // Request permission when the component mounts
    requestPermission();
  }, []);

  return (
    <WidePage title={t('aslaa')}>
      <LandingHeader />
      <LandingHero />
      <LandingFeature />
      <LandingDownload />
      <LandingAbout />
    </WidePage>
  );
}
