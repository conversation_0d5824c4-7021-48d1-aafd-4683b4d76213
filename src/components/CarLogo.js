import PropTypes from 'prop-types';
import { Link as RouterLink } from 'react-router-dom';
// @mui
import { useTheme } from '@mui/material/styles';
import { Box } from '@mui/material';

// ----------------------------------------------------------------------

CarLogo.propTypes = {
    disabledLink: PropTypes.bool,
    sx: PropTypes.object,
};

export default function CarLogo({ disabledLink = false, sx }) {

    const logo = (
        <Box sx={{ height: 80, ...sx }} padding={2}>
            <img width="100%" height="100%" src="/images/logo-dark.png" alt="logo" />
        </Box>
    );

    if (disabledLink) {
        return <>{logo}</>;
    }

    return <RouterLink to="/home">{logo}</RouterLink>;
}
