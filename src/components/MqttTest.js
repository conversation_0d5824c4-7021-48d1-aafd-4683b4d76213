import React, { useEffect, useState } from 'react';
import { Box, Button, TextField, Typography, Paper, List, ListItem, Divider } from '@mui/material';
import * as mqttService from '../services/mqtt';

const MqttTest = () => {
  const [connected, setConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [publishTopic, setPublishTopic] = useState('aslaa/test');
  const [publishMessage, setPublishMessage] = useState('Hello MQTT');
  const [subscribeTopic, setSubscribeTopic] = useState('aslaa/test');

  useEffect(() => {
    // Initialize MQTT connection
    mqttService.initMqtt();

    // Set up event listeners
    mqttService.on('connect', () => {
      setConnected(true);
      addMessage('System', 'Connected to MQTT broker');
    });

    mqttService.on('message', (topic, message) => {
      addMessage(topic, message.toString());
    });

    mqttService.on('error', (error) => {
      addMessage('Error', error.message);
    });

    mqttService.on('disconnect', () => {
      setConnected(false);
      addMessage('System', 'Disconnected from MQTT broker');
    });

    // Clean up on component unmount
    return () => {
      mqttService.disconnect();
    };
  }, []);

  const addMessage = (topic, message) => {
    setMessages(prev => [
      ...prev,
      {
        id: Date.now(),
        topic,
        message,
        time: new Date().toLocaleTimeString()
      }
    ]);
  };

  const handleSubscribe = () => {
    if (subscribeTopic) {
      mqttService.subscribe(subscribeTopic);
      addMessage('System', `Subscribed to ${subscribeTopic}`);
    }
  };

  const handlePublish = () => {
    if (publishTopic && publishMessage) {
      mqttService.publish(publishTopic, publishMessage);
      addMessage('System', `Published to ${publishTopic}: ${publishMessage}`);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          MQTT Connection Test
        </Typography>
        <Typography variant="body1" gutterBottom>
          Status: {connected ?
            <span style={{ color: 'green' }}>Connected</span> :
            <span style={{ color: 'red' }}>Disconnected</span>}
        </Typography>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>Subscribe</Typography>
          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <TextField
              label="Topic"
              variant="outlined"
              size="small"
              fullWidth
              value={subscribeTopic}
              onChange={(e) => setSubscribeTopic(e.target.value)}
            />
            <Button
              variant="contained"
              onClick={handleSubscribe}
              disabled={!connected || !subscribeTopic}
            >
              Subscribe
            </Button>
          </Box>
        </Box>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>Publish</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <TextField
              label="Topic"
              variant="outlined"
              size="small"
              value={publishTopic}
              onChange={(e) => setPublishTopic(e.target.value)}
            />
            <TextField
              label="Message"
              variant="outlined"
              size="small"
              value={publishMessage}
              onChange={(e) => setPublishMessage(e.target.value)}
            />
            <Button
              variant="contained"
              onClick={handlePublish}
              disabled={!connected || !publishTopic || !publishMessage}
            >
              Publish
            </Button>
          </Box>
        </Box>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Messages
        </Typography>
        <List>
          {messages.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No messages yet
            </Typography>
          ) : (
            messages.map((msg, index) => (
              <React.Fragment key={msg.id}>
                {index > 0 && <Divider />}
                <ListItem>
                  <Box sx={{ width: '100%' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="subtitle2" color="primary">
                        {msg.topic}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {msg.time}
                      </Typography>
                    </Box>
                    <Typography variant="body2">
                      {msg.message}
                    </Typography>
                  </Box>
                </ListItem>
              </React.Fragment>
            ))
          )}
        </List>
      </Paper>
    </Box>
  );
};

export default MqttTest;
