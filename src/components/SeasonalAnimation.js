import React, { useState, useEffect, useMemo } from 'react';
import Snowfall from 'react-snowfall';

// Custom falling animation component for non-snow effects
const FallingAnimation = ({
  elements,
  count = 50,
  speed = [0.5, 2],
  wind = [-0.5, 0.5],
  rotationSpeed = [-1, 1],
  style = {}
}) => {
  const [particles, setParticles] = useState([]);

  useEffect(() => {
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: -50 - Math.random() * 100,
      element: elements[Math.floor(Math.random() * elements.length)],
      speed: Math.random() * (speed[1] - speed[0]) + speed[0],
      windSpeed: Math.random() * (wind[1] - wind[0]) + wind[0],
      rotation: Math.random() * 360,
      rotationSpeed: Math.random() * (rotationSpeed[1] - rotationSpeed[0]) + rotationSpeed[0],
      size: 0.8 + Math.random() * 0.4, // Random size between 0.8 and 1.2
    }));
    setParticles(newParticles);
  }, [count, elements, speed, wind, rotationSpeed]);

  useEffect(() => {
    const animateParticles = () => {
      setParticles(prevParticles =>
        prevParticles.map(particle => {
          let newY = particle.y + particle.speed;
          let newX = particle.x + particle.windSpeed;
          let newRotation = particle.rotation + particle.rotationSpeed;

          // Reset particle when it goes off screen
          if (newY > window.innerHeight + 50) {
            newY = -50 - Math.random() * 100;
            newX = Math.random() * window.innerWidth;
          }

          // Wrap around horizontally
          if (newX > window.innerWidth + 50) {
            newX = -50;
          } else if (newX < -50) {
            newX = window.innerWidth + 50;
          }

          return {
            ...particle,
            x: newX,
            y: newY,
            rotation: newRotation,
          };
        })
      );
    };

    const interval = setInterval(animateParticles, 100); // 10 FPS for smoother, less distracting animation
    return () => clearInterval(interval);
  }, []);

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: -1, // Behind everything
        filter: 'blur(1px)', // Add blur effect
        ...style,
      }}
    >
      {particles.map(particle => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            left: particle.x,
            top: particle.y,
            fontSize: `${particle.size * 16}px`, // Smaller size
            transform: `rotate(${particle.rotation}deg)`,
            userSelect: 'none',
            opacity: 0.3, // Much more transparent
          }}
        >
          {particle.element}
        </div>
      ))}
    </div>
  );
};

/**
 * Seasonal Animation Component
 *
 * Automatically displays different animations based on the current season:
 * - Winter (Dec, Jan, Feb): Snow animation
 * - Spring (Mar, Apr, May): Falling flower petals
 * - Summer (Jun, Jul, Aug): Rain drops
 * - Autumn (Sep, Oct, Nov): Falling leaves
 *
 * All animations are subtle, blurred, and positioned behind the UI to avoid interference.
 *
 * @param {string} forceSeasonOverride - Optional override for testing ('winter', 'spring', 'summer', 'autumn')
 */
const SeasonalAnimation = ({ forceSeasonOverride = null }) => {
  const getCurrentSeason = () => {
    if (forceSeasonOverride) return forceSeasonOverride;

    const month = new Date().getMonth(); // 0-11

    // Define seasons by month
    if (month >= 11 || month <= 1) return 'winter'; // Dec, Jan, Feb
    if (month >= 2 && month <= 4) return 'spring';  // Mar, Apr, May
    if (month >= 5 && month <= 7) return 'summer';  // Jun, Jul, Aug
    if (month >= 8 && month <= 10) return 'autumn'; // Sep, Oct, Nov

    return 'winter'; // fallback
  };

  const season = getCurrentSeason();

  const seasonalConfig = useMemo(() => {
    switch (season) {
      case 'winter':
        return {
          type: 'snow',
          color: 'rgba(255, 255, 255, 0.4)', // More transparent snow
          count: 25, // Fewer particles
        };

      case 'spring':
        return {
          type: 'petals',
          elements: ['🌸', '🌺', '🌼', '🌻', '🌷'],
          count: 15, // Fewer particles
          speed: [0.2, 1.0], // Slower movement
          wind: [-0.2, 0.2], // Less wind
          rotationSpeed: [-0.3, 0.3], // Slower rotation
        };

      case 'summer':
        return {
          type: 'rain',
          elements: ['💧', '💦'],
          count: 20, // Much fewer rain drops
          speed: [1.5, 3], // Slightly slower
          wind: [-0.1, 0.1], // Less wind
          rotationSpeed: [0, 0], // No rotation for rain
        };

      case 'autumn':
        return {
          type: 'leaves',
          elements: ['🍂', '🍁', '🍃'],
          count: 20, // Fewer leaves
          speed: [0.3, 1.5], // Slower falling
          wind: [-0.5, 0.5], // Less wind
          rotationSpeed: [-1, 1], // Slower rotation
        };

      default:
        return {
          type: 'snow',
          color: 'rgba(255, 255, 255, 0.4)',
          count: 25,
        };
    }
  }, [season]);

  // Render appropriate animation based on season
  if (seasonalConfig.type === 'snow') {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: -1, // Behind everything
          filter: 'blur(1px)', // Add blur effect
          opacity: 0.6, // Make it more transparent
          pointerEvents: 'none',
        }}
      >
        <Snowfall
          color={seasonalConfig.color}
          snowflakeCount={seasonalConfig.count}
          speed={[0.5, 1.5]} // Slower snow
          wind={[-0.2, 0.2]} // Less wind
          radius={[0.5, 2]} // Smaller snowflakes
        />
      </div>
    );
  }

  return (
    <FallingAnimation
      elements={seasonalConfig.elements}
      count={seasonalConfig.count}
      speed={seasonalConfig.speed}
      wind={seasonalConfig.wind}
      rotationSpeed={seasonalConfig.rotationSpeed}
    />
  );
};

export default SeasonalAnimation;
