import styled from "@emotion/styled";
import { Card, Typography } from "@mui/material";
import Box from "@mui/material/Box";
import Iconify from "./Iconify";
// -------------------------------------------
const RootStyle = styled('div')(() => ({
    right: 0,
    bottom: 0,
    zIndex: 99999,
    width: '100%',
    height: '100%',
    position: 'fixed',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor:'#061c2ad1'
  }));
export default function FindingScreen({ message, description}) {
    return (
    <RootStyle>
        <Card  sx={{ width: 300, minHeight: 100, border: '1px solid', borderColor: 'grey.50048', p: 4, textAlign: 'center', backgroundColor: 'transparent' }} >

            <Typography variant="subtitle1">
                {message}
            </Typography>
            <Typography variant="subtitle2">
                {description}
            </Typography>
        </Card>
    </RootStyle>
    )
}