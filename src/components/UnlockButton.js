import PropTypes from 'prop-types';
import Button from './Button';

UnlockButton.propTypes = {
  sx: PropTypes.object,
  handleUnlock: PropTypes.func.isRequired,
};

export default function UnlockButton({ sx, handleUnlock }) {
  const svgContent = (
    <>
      <g transform="translate(125,60) rotate(180 0 0) scale(3,-3)">
        <path fill="white" stroke="none" d="M4 8V6a6 6 0 1 1 12 0h-3v2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2h1zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0zM7 6v2h6V6a3 3 0 0 0-6 0z" />
      </g>
      <path d="M0 200 h100 A100 100 0 0 1 200 100 v -100 A 200 200 0 0 0 0 200" />
    </>
  );

  return <Button sx={sx} onClick={handleUnlock} svgContent={svgContent} />;
}
