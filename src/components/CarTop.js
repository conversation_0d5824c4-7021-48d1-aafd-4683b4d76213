import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next'
// @mui

import { Box } from '@mui/material';

// ----------------------------------------------------------------------

CarTop.propTypes = {
  sx: PropTypes.object,
  device: PropTypes.object,
  action: PropTypes.string,
  status: PropTypes.object,
  color: PropTypes.string,
  title: PropTypes.string,
};



export default function CarTop({ sx, device, status, action, color, title = "" }) {
  const { t } = useTranslation();
   
  const carTopSvg = (

    <Box sx={{ position: "relative", width: 'inherit', height: 'inherit', ...sx }} >
      <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
        width="100%" height="100%" viewBox="0 0 50 100" style={{ filter: `drop-shadow(0px 0px 5px ${color})` }}
      >
        <defs>
          <radialGradient id="radial-gradient" cx="381.1027" cy="126.5375" fx="381.1027" fy="126.5375" r="124.7596" gradientUnits="userSpaceOnUse">
            <stop offset=".0767" stopColor="#fff" stopOpacity=".8" />
            <stop offset=".2792" stopColor="#bccfd0" stopOpacity=".5857" />
            <stop offset=".5951" stopColor="#5a888b" stopOpacity=".2725" />
            <stop offset=".8198" stopColor="#1d5c60" stopOpacity=".0758" />
            <stop offset=".9302" stopColor="#064c50" stopOpacity="0" />
          </radialGradient>
          <linearGradient id="linear-gradient" x1="378.0114" y1="124.6879" x2="401.2247" y2="160.7118" gradientUnits="userSpaceOnUse">
            <stop offset="0" stopColor="#fff" />
            <stop offset=".3414" stopColor="#e4feff" stopOpacity=".5904" />
            <stop offset=".6289" stopColor="#d0feff" stopOpacity=".2729" />
            <stop offset=".831" stopColor="#c3feff" stopOpacity=".0759" />
            <stop offset=".9302" stopColor="#bffeff" stopOpacity="0" />
          </linearGradient>
          <radialGradient id="radial-gradient-2" cx="637.3063" cy="127.0848" fx="637.3063" fy="127.0848" r="124.7596" gradientTransform="translate(1241.6362) rotate(-180) scale(1 -1)" xlinkHref="#radial-gradient" />
          <linearGradient id="linear-gradient-2" x1="634.2149" y1="125.2352" x2="657.4283" y2="161.2591" gradientTransform="translate(1241.6362) rotate(-180) scale(1 -1)" xlinkHref="#linear-gradient" />
          <radialGradient id="radial-gradient-3" cx="224.266" cy="944.1764" fx="224.266" fy="944.1764" r="69.8157" gradientTransform="translate(-100.0815 1488.934) rotate(-127.437) scale(.7636)" gradientUnits="userSpaceOnUse">
            <stop offset=".0077" stopColor="red" stopOpacity=".9" />
            <stop offset=".308" stopColor="#ff1e18" stopOpacity=".5565" />
            <stop offset=".8062" stopColor="#ff4f3f" stopOpacity="0" />
          </radialGradient>
          <radialGradient id="radial-gradient-4" cx="45.7285" cy="1177.3911" fx="45.7285" fy="1177.3911" r="69.8157" gradientTransform="translate(1308.7616 1488.934) rotate(-52.563) scale(.7636 -.7636)" xlinkHref="#radial-gradient-3" />
        </defs>
        <g transform="translate(0,102) scale(0.01,-0.01)"
          fill={color} stroke="none">
          <path d="M1660 9490 c-11 -11 -27 -20 -36 -20 -15 0 -32 -5 -184 -50 -182 -54
-376 -185 -562 -379 -223 -232 -296 -381 -242 -494 13 -28 15 -50 10 -110 -7
-72 -9 -76 -39 -88 -18 -8 -49 -29 -69 -47 l-38 -34 0 -751 c0 -799 1 -822 47
-833 23 -6 23 -6 23 -179 l0 -173 -116 -64 c-179 -97 -247 -169 -228 -243 11
-46 47 -60 144 -58 47 1 111 9 143 16 l57 14 0 -1428 0 -1427 -30 -7 c-16 -3
-41 -15 -55 -26 l-25 -20 0 -875 0 -874 28 -11 c15 -6 44 -12 65 -13 l39 -1
57 -145 c32 -80 67 -164 79 -186 12 -23 18 -44 15 -48 -4 -3 5 -19 19 -34 13
-15 41 -50 62 -79 56 -79 126 -137 193 -160 32 -11 120 -34 196 -52 75 -18
152 -40 170 -50 128 -73 434 -104 1047 -104 622 -1 952 33 1067 108 20 13 119
40 248 67 28 6 79 21 115 34 71 26 135 83 219 194 23 30 57 74 75 97 19 23 49
79 66 125 55 143 107 238 131 238 11 0 29 5 40 10 19 10 19 31 19 886 0 824
-1 875 -17 881 -10 4 -21 12 -26 19 -4 7 -21 15 -37 19 l-30 7 0 1427 0 1428
63 -14 c93 -22 216 -22 250 0 50 32 48 92 -5 152 -40 46 -89 80 -211 145 l-97
52 0 174 c0 159 2 174 18 174 9 0 25 7 35 14 16 12 17 62 17 807 l-1 794 -68
34 -68 34 -7 79 c-5 65 -4 82 11 105 11 16 17 45 17 78 0 66 -49 170 -129 270
-85 107 -249 269 -344 341 -135 101 -318 188 -439 209 -23 4 -44 11 -48 16 -3
5 -19 9 -36 9 -21 0 -31 6 -35 20 -5 20 -14 20 -774 20 -756 0 -769 0 -789
-20z m180 -19 c0 -25 -21 -61 -36 -61 -11 0 -14 5 -9 18 4 9 10 25 12 35 5 18
33 24 33 8z m65 -30 c-4 -17 -11 -31 -16 -31 -11 0 -11 0 2 39 14 39 23 34 14
-8z m179 12 c-9 -39 -13 -43 -35 -43 -15 0 -19 7 -19 35 0 32 2 35 30 35 28 0
30 -2 24 -27z m156 17 c0 -24 -25 -60 -42 -60 -14 0 -18 8 -18 35 0 32 2 35
30 35 17 0 30 -4 30 -10z m165 1 c3 -5 0 -21 -6 -35 -16 -35 -64 -37 -73 -3
-11 42 -7 47 33 47 22 0 42 -4 46 -9z m145 -26 c0 -32 -3 -35 -28 -35 -34 0
-52 16 -52 47 0 20 5 23 40 23 39 0 40 -1 40 -35z m140 0 c0 -27 -4 -35 -18
-35 -17 0 -42 36 -42 60 0 6 14 10 30 10 28 0 30 -3 30 -35z m150 0 c0 -28 -4
-35 -19 -35 -20 0 -31 18 -31 51 0 14 7 19 25 19 22 0 25 -4 25 -35z m146 -10
c4 -21 3 -27 -4 -20 -6 6 -14 24 -18 40 -4 21 -3 27 4 20 6 -5 14 -23 18 -40z
m83 14 c14 -40 14 -39 -3 -39 -8 0 -19 14 -25 31 -14 40 -14 39 3 39 8 0 19
-14 25 -31z m-1093 -36 c-14 -14 -23 4 -13 27 l10 25 5 -23 c2 -13 1 -26 -2
-29z m943 5 c1 -5 -3 -8 -9 -8 -5 0 -10 12 -9 28 0 24 1 25 9 7 5 -11 9 -23 9
-27z m-1484 -83 c-107 -52 -195 -86 -195 -75 0 23 214 120 260 119 17 0 -2
-14 -65 -44z m2021 20 c78 -29 184 -84 184 -96 0 -4 3 -16 7 -25 5 -13 -8 -8
-43 17 -61 45 -113 71 -187 94 -32 10 -60 22 -63 26 -10 16 35 9 102 -16z
m-288 -32 l3 -33 -735 0 c-582 0 -736 3 -736 13 0 6 3 22 6 35 l6 22 727 -2
726 -3 3 -32z m232 4 c47 -18 124 -58 171 -90 l86 -56 -4 -53 c-2 -29 -9 -77
-14 -106 -9 -42 -14 -52 -27 -48 -9 3 -59 14 -111 26 -97 20 -123 35 -147 82
-31 59 -14 62 39 5 29 -31 58 -57 65 -57 20 0 13 16 -12 28 -13 6 -37 29 -52
51 -16 22 -36 43 -45 46 -16 6 -39 66 -39 104 0 12 -6 37 -14 56 -24 55 -13
57 104 12z m-1828 -39 c-63 -263 -59 -257 -216 -292 l-108 -23 -10 26 c-5 14
-12 69 -15 123 l-6 96 31 16 c18 9 44 18 59 20 16 2 55 16 88 31 95 44 148 64
171 65 20 0 20 -1 6 -62z m1673 3 c-10 -9 -25 14 -24 37 l1 27 13 -30 c7 -16
12 -32 10 -34z m-1599 1 c-12 -11 -18 7 -10 30 l8 23 4 -23 c2 -13 1 -26 -2
-30z m64 -65 c0 -1 -28 -27 -62 -57 -60 -53 -61 -53 -55 -25 15 75 23 85 72
85 25 0 45 -1 45 -3z m1540 -1 c0 -2 7 -21 16 -42 l16 -39 -43 40 c-24 22 -46
41 -48 43 -2 1 10 2 27 2 18 0 32 -2 32 -4z m672 -188 c48 -48 108 -116 134
-150 41 -53 33 -47 -51 40 -98 103 -186 174 -257 208 -33 15 -38 22 -38 51 l0
34 63 -47 c34 -27 101 -88 149 -136z m-2762 157 c0 -8 -4 -15 -10 -15 -11 0
-199 -161 -249 -214 -19 -20 -40 -36 -45 -36 -14 0 143 159 223 226 69 57 81
63 81 39z m2003 -54 c76 -53 207 -205 207 -241 0 -7 9 -23 20 -37 30 -39 91
-205 180 -493 103 -332 138 -470 190 -740 56 -294 120 -620 127 -650 6 -22 0
-19 -45 24 -89 86 -168 131 -397 229 -187 80 -357 126 -590 159 -143 20 -729
17 -876 -5 -237 -35 -395 -83 -645 -195 -143 -63 -220 -110 -296 -181 -34 -31
-59 -48 -55 -37 4 10 32 150 62 310 70 368 119 605 151 726 32 121 165 551
205 667 78 221 185 382 313 467 l39 26 684 0 684 0 42 -29z m-1999 -13 c3 -18
7 -54 10 -80 3 -27 8 -58 11 -71 5 -19 0 -25 -27 -35 -18 -6 -69 -32 -113 -58
-158 -91 -214 -130 -227 -160 -6 -13 -14 -23 -17 -21 -3 2 -21 10 -38 17 -18
8 -33 18 -33 23 0 30 43 86 123 159 143 132 289 258 298 258 4 0 10 -15 13
-32z m2595 -18 c96 -48 254 -198 342 -324 26 -38 31 -50 20 -54 -8 -3 -25 -15
-37 -27 l-22 -22 -64 64 c-74 74 -181 151 -260 186 l-56 25 8 39 c5 21 12 59
15 86 3 26 8 47 11 47 3 0 22 -9 43 -20z m-205 -174 c124 -24 265 -98 363
-190 l80 -75 13 -78 c6 -42 14 -199 16 -347 2 -149 8 -350 15 -446 12 -180 9
-294 -16 -645 -18 -238 -19 -339 -5 -330 6 3 10 43 10 88 0 45 5 143 10 217
20 260 27 487 19 640 -5 85 -11 288 -14 450 -3 162 -9 320 -14 350 -5 30 -7
57 -5 59 2 3 20 -11 39 -29 l35 -34 0 -952 c0 -879 1 -952 17 -958 9 -4 20 -3
25 2 5 5 8 429 8 942 0 532 4 930 9 925 9 -11 29 -237 67 -790 27 -388 27
-375 0 -961 -4 -92 -33 -153 -92 -196 -44 -31 -54 -51 -33 -63 20 -13 55 5
102 51 l47 46 0 -127 0 -127 -29 5 c-16 3 -39 9 -52 12 -14 3 -32 0 -44 -9
-29 -22 -9 -51 36 -51 96 -2 429 -207 429 -265 0 -23 -44 -35 -79 -21 -14 6
-82 24 -151 41 -69 16 -146 40 -173 54 -26 13 -50 19 -53 14 -9 -14 93 -53
214 -83 62 -15 111 -29 109 -31 -2 -3 -60 7 -128 22 -178 38 -248 81 -225 139
4 12 14 14 41 9 27 -5 36 -3 41 11 10 24 -9 37 -63 45 -51 8 -73 -1 -73 -30 0
-9 -30 -44 -67 -77 -120 -107 -243 -301 -302 -478 -42 -125 -65 -237 -100
-475 -51 -352 -56 -415 -55 -655 0 -201 3 -243 23 -335 13 -58 25 -149 26
-205 2 -55 4 -128 5 -162 2 -72 16 -75 26 -5 14 97 28 104 266 137 101 14 210
32 243 40 33 8 66 14 73 15 9 0 12 -283 12 -1383 l0 -1383 -44 -111 c-25 -61
-53 -128 -64 -150 l-20 -39 -109 -12 c-214 -24 -437 -81 -457 -117 -37 -68
-62 -96 -109 -120 l-51 -25 -925 0 c-858 0 -930 1 -975 17 -53 19 -76 53 -76
112 0 27 -5 31 -63 50 -101 33 -233 62 -372 80 l-130 17 -22 39 c-13 22 -52
114 -88 205 l-65 165 0 1353 0 1352 30 0 c17 0 48 -9 70 -20 22 -11 47 -20 57
-20 29 0 190 -54 293 -97 52 -23 106 -44 120 -48 32 -9 80 -61 80 -86 0 -10 5
-19 10 -19 6 0 10 40 10 100 0 60 -4 100 -10 100 -5 0 -10 -16 -10 -35 0 -19
-1 -35 -3 -35 -18 0 -125 38 -173 61 -34 16 -84 36 -110 43 -27 8 -92 30 -144
49 -52 19 -117 42 -145 52 -27 10 -56 22 -63 28 -18 14 -18 1867 1 1867 6 0
29 3 49 6 l38 7 0 -851 c0 -721 2 -851 14 -855 8 -3 17 -19 21 -36 21 -98 295
-271 427 -271 75 0 90 15 114 110 32 124 44 244 44 430 0 179 -7 253 -46 550
-73 556 -158 777 -394 1025 -47 50 -90 99 -96 110 -20 35 -139 14 -132 -23 3
-13 13 -17 46 -15 40 2 42 0 42 -27 0 -47 -9 -60 -38 -54 -16 3 -54 -5 -94
-20 -61 -23 -131 -36 -300 -53 -72 -7 -75 5 -15 59 101 94 317 211 391 212 34
1 42 5 44 23 5 32 -29 47 -76 33 -20 -5 -40 -10 -44 -10 -5 0 -8 55 -8 123 l0
123 50 -48 c33 -31 59 -48 75 -48 40 0 30 27 -29 83 -77 74 -83 100 -92 437
-5 151 -7 313 -6 360 6 191 64 1003 79 1105 5 28 10 -375 13 -895 5 -937 5
-945 25 -945 20 0 20 8 25 957 l5 957 44 37 44 38 -7 -36 c-18 -97 -48 -326
-56 -433 -15 -190 -11 -961 6 -1170 8 -99 17 -196 20 -216 5 -29 0 -45 -21
-78 -29 -43 -28 -75 2 -69 10 2 50 50 91 108 40 58 91 124 114 147 50 52 188
151 300 216 219 126 749 237 1133 237 265 0 658 -66 928 -154 208 -69 460
-242 558 -383 l54 -78 -60 60 c-94 95 -307 248 -430 310 -128 63 -262 103
-483 142 -150 27 -172 28 -472 28 -250 0 -343 -4 -450 -18 -343 -47 -625 -124
-835 -230 -113 -56 -180 -104 -256 -183 -78 -82 -215 -262 -205 -271 3 -3 8
-4 9 -2 2 2 39 51 82 109 93 124 172 204 264 264 306 202 816 320 1386 320
258 0 389 -13 603 -59 280 -61 426 -133 666 -329 145 -118 226 -229 226 -308
0 -10 5 -18 10 -18 16 0 12 35 -10 92 l-20 53 23 -29 c16 -20 29 -27 40 -23
24 9 21 33 -7 70 -13 18 -33 51 -44 73 -12 23 -33 55 -47 72 -15 18 -24 36
-21 41 4 5 -7 70 -23 143 -16 73 -55 266 -86 428 -31 162 -74 369 -97 460 -65
268 -233 812 -274 893 l-15 27 46 0 c25 0 82 -12 126 -26 81 -27 106 -25 93 9
-11 29 -172 77 -261 77 -26 0 -36 6 -47 29 l-15 29 37 -9 c20 -4 73 -15 116
-23z m-2099 0 c-13 -20 -27 -25 -86 -30 -126 -13 -219 -49 -219 -85 0 -29 20
-30 76 -4 29 13 81 26 117 30 l65 6 -35 -94 c-47 -124 -184 -575 -234 -766
-21 -84 -57 -246 -79 -360 -23 -115 -64 -320 -92 -458 -28 -137 -52 -259 -52
-270 -2 -21 -56 -114 -67 -115 -26 -1 -41 378 -36 930 4 505 5 532 31 700 37
250 44 268 116 332 72 63 163 120 239 149 57 22 208 57 249 58 21 1 22 0 7
-23z m2705 -198 c0 -17 -10 -6 -25 25 l-16 32 20 -25 c11 -14 21 -28 21 -32z
m-3411 -67 c16 -10 6 -51 -14 -51 -40 0 -57 24 -33 48 14 14 28 15 47 3z
m3431 -23 c17 -28 18 -34 5 -41 -35 -19 -88 30 -64 59 18 22 38 16 59 -18z
m-3550 -440 c0 -47 -30 -449 -45 -608 -16 -173 -18 -335 -7 -535 9 -161 4
-245 -14 -245 -12 0 -14 105 -14 705 l0 705 40 0 c35 0 40 -3 40 -22z m3690
-683 c0 -634 -2 -705 -16 -705 -14 0 -15 33 -9 318 6 278 5 345 -13 542 -12
124 -24 275 -27 335 -3 61 -8 134 -11 163 l-5 52 40 0 41 0 0 -705z m-3349
-1557 c103 -137 185 -336 228 -549 28 -137 78 -500 92 -664 19 -223 6 -433
-37 -617 -14 -60 -19 -67 -45 -73 -43 -10 -125 18 -219 75 -104 62 -134 90
-161 149 -11 25 -25 54 -30 64 -6 11 -9 344 -9 855 0 772 2 840 18 879 l17 42
48 -47 c27 -26 71 -77 98 -114z m3119 -762 l0 -832 -30 -59 c-39 -75 -77 -115
-162 -166 -127 -77 -214 -104 -251 -76 -42 30 -83 356 -72 567 12 246 73 684
119 869 59 234 155 416 300 569 l49 53 24 -46 23 -46 0 -833z m-3350 885 c0
-5 -21 -12 -46 -16 -26 -3 -54 -9 -63 -12 -9 -4 -13 -3 -9 1 9 11 79 35 101
36 9 0 17 -4 17 -9z m-275 -62 c-22 -4 -56 -8 -75 -7 l-35 0 30 8 c17 4 50 8
75 8 l45 0 -40 -9z m3730 -988 l0 -973 -125 -35 c-116 -31 -318 -69 -400 -74
-44 -3 -56 4 -62 34 -4 22 -3 23 73 22 74 0 81 2 185 55 86 43 124 69 177 122
37 37 67 76 67 87 0 11 7 26 15 35 13 13 15 122 15 861 l0 846 28 -3 27 -3 0
-974z m55 -1 c0 -963 0 -970 -20 -970 -20 0 -20 7 -20 970 0 963 0 970 20 970
20 0 20 -7 20 -970z m-3 -2317 c-2 -720 -6 -1327 -9 -1351 -3 -24 -10 -48 -17
-55 -8 -8 -11 348 -11 1344 0 746 3 1359 7 1362 33 34 33 36 30 -1300z m-3720
-941 c-1 -229 -2 -41 -2 418 0 459 1 647 2 417 2 -229 2 -605 0 -835z m3833
418 c0 -547 -3 -830 -10 -830 -7 0 -10 283 -10 830 0 547 3 830 10 830 7 0 10
-283 10 -830z m-3790 -11 c0 -539 -3 -819 -10 -819 -7 0 -10 282 -10 826 0
547 3 823 10 819 7 -4 10 -290 10 -826z m3758 9 c-2 -646 -6 -822 -15 -826
-10 -3 -13 165 -13 816 0 450 3 822 7 825 3 4 10 7 15 7 4 0 7 -370 6 -822z
m-47 -905 c-12 -20 -14 -14 -5 12 4 9 9 14 11 11 3 -2 0 -13 -6 -23z m-111
-283 c-12 -23 -24 -40 -26 -37 -3 3 4 24 16 47 12 23 24 40 26 37 3 -3 -4 -24
-16 -47z m-186 -134 c-10 -26 -138 -116 -149 -104 -2 2 -6 24 -10 49 l-7 47
64 10 c94 14 108 14 102 -2z m14 -32 c-33 -46 -93 -101 -135 -123 -28 -14 -33
-14 -33 3 0 8 26 30 58 50 31 19 75 53 97 75 21 23 41 41 43 41 2 0 -12 -21
-30 -46z m-3033 17 c42 -11 79 -22 82 -25 3 -3 3 -17 -1 -31 -5 -19 -13 -25
-33 -25 -47 0 -104 23 -129 53 -45 53 -35 57 81 28z m90 -1 c-3 -5 -12 -7 -20
-3 -21 7 -19 13 6 13 11 0 18 -4 14 -10z m2709 -27 c3 -10 7 -35 10 -55 6 -36
4 -39 -30 -53 -19 -8 -75 -17 -123 -21 l-89 -7 17 54 18 53 79 22 c98 28 111
28 118 7z m-2577 -14 c15 -7 50 -17 78 -24 44 -10 50 -15 58 -46 5 -19 13 -39
19 -46 27 -33 -164 -30 -209 3 -28 21 -42 76 -29 110 6 17 45 18 83 3z m-155
-71 c-5 -5 -19 -5 -35 1 -37 14 -34 18 9 13 22 -3 32 -8 26 -14z m8 -28 c0 -5
-2 -10 -4 -10 -3 0 -8 5 -11 10 -3 6 -1 10 4 10 6 0 11 -4 11 -10z m2738 -12
c-5 -13 -48 -25 -216 -62 l-103 -23 26 27 c24 25 35 28 121 34 53 3 110 13
127 20 41 18 50 19 45 4z m-2408 -48 c11 -7 1 -10 -35 -10 -36 0 -46 3 -35 10
8 5 24 10 35 10 11 0 27 -5 35 -10z m1890 -35 c9 -11 10 -19 3 -26 -7 -7
-1138 -13 -1730 -9 -14 0 -18 32 -6 43 3 4 392 7 863 7 761 0 859 -2 870 -15z
m-560 -97 c-72 -12 -461 -12 -525 0 -36 7 41 10 265 10 226 0 299 -3 260 -10z"/>
          <path d="M3095 9074 c11 -9 27 -21 36 -27 27 -20 87 -106 109 -155 29 -63 152
-391 178 -472 12 -36 50 -182 86 -325 36 -143 88 -336 116 -428 47 -158 78
-326 65 -347 -4 -6 -1 -10 4 -10 16 0 13 99 -5 187 -8 42 -31 126 -50 187 -19
61 -66 236 -105 389 -38 153 -83 322 -100 375 -44 137 -153 412 -195 493 -38
72 -111 149 -141 149 -16 -1 -16 -2 2 -16z"/>
          <path d="M1691 9014 c-16 -14 -44 -48 -60 -77 -34 -59 -130 -291 -173 -417
-26 -78 -200 -765 -247 -975 -11 -49 -29 -119 -40 -155 -22 -70 -25 -90 -13
-90 4 0 12 19 18 43 6 23 23 85 36 137 14 52 65 257 113 455 109 451 140 559
221 765 78 196 126 286 165 309 16 9 29 19 29 24 0 14 -19 7 -49 -19z"/>
          <path d="M1955 7513 c-176 -22 -505 -108 -611 -158 -84 -40 -153 -108 -133
-131 16 -20 20 -18 64 22 46 42 85 63 111 62 10 -1 60 11 110 25 95 28 319 70
437 82 37 4 67 12 67 17 0 10 -33 7 -192 -18 -54 -9 -103 -14 -110 -12 -18 6
191 46 312 59 104 12 123 20 113 46 -5 13 -91 16 -168 6z m-278 -119 c-3 -3
-12 -4 -19 -1 -8 3 -5 6 6 6 11 1 17 -2 13 -5z"/>
          <path d="M2752 7498 c3 -20 11 -24 63 -30 177 -22 245 -34 405 -74 253 -62
310 -85 388 -158 30 -28 33 -29 48 -14 14 14 15 19 2 37 -50 71 -164 122 -410
185 -185 48 -351 76 -442 76 -54 0 -57 -1 -54 -22z"/>
          <path d="M2954 7399 c-3 -5 6 -9 20 -9 14 0 85 -15 158 -34 73 -19 160 -39
193 -46 33 -6 79 -22 103 -35 45 -25 59 -16 16 11 -27 18 -366 107 -441 117
-24 3 -46 1 -49 -4z"/>
          <path d="M2025 7149 c-132 -4 -283 -14 -335 -22 -349 -58 -646 -233 -750 -443
-28 -57 -35 -83 -38 -150 -8 -145 30 -249 223 -614 30 -58 73 -161 96 -230 53
-159 87 -227 139 -274 38 -34 43 -36 58 -23 30 27 259 126 350 152 l88 26 605
-3 604 -3 85 -31 c111 -41 276 -118 300 -139 17 -16 20 -15 48 5 62 46 89 98
153 289 36 105 74 192 143 320 150 279 182 376 174 527 -6 121 -41 189 -152
299 -165 161 -409 268 -678 294 -316 32 -573 36 -1113 20z m940 -82 c-70 -51
-269 -222 -390 -333 -129 -119 -877 -852 -883 -866 -16 -36 71 45 420 390 431
425 574 559 745 698 59 48 118 98 130 111 22 23 24 23 115 12 116 -14 290 -58
387 -99 255 -105 414 -273 428 -452 8 -101 -15 -169 -162 -468 -81 -164 -151
-323 -175 -394 -22 -65 -51 -138 -66 -163 -32 -56 -35 -55 -205 22 -225 102
-162 95 -883 95 l-631 0 -75 -29 c-41 -16 -126 -52 -189 -80 -62 -28 -121 -51
-130 -51 -27 0 -58 58 -106 200 -29 86 -91 225 -170 385 -133 266 -160 332
-170 417 -22 181 129 383 367 492 135 61 324 114 468 129 19 2 298 4 620 5
l585 1 -30 -22z"/>
          <path d="M2468 6957 c-109 -67 -208 -150 -325 -270 -132 -136 -428 -486 -418
-495 3 -4 14 3 23 14 43 54 236 280 324 380 142 161 295 293 420 364 32 17 61
36 64 41 12 20 -24 6 -88 -34z"/>
          <path d="M2954 6764 c-108 -94 -686 -675 -679 -683 3 -3 148 137 323 310 174
173 349 343 387 376 39 34 67 64 64 67 -3 4 -46 -28 -95 -70z"/>
          <path d="M1924 6743 c-30 -20 -127 -133 -121 -140 3 -3 39 29 80 71 71 73 87
101 41 69z"/>
          <path d="M3050 6539 c-19 -12 -51 -34 -71 -50 -45 -35 -224 -239 -210 -239 5
0 29 21 53 47 114 125 176 186 214 211 23 15 50 32 60 39 35 22 -9 14 -46 -8z"/>
          <path d="M1850 5510 c-10 -6 185 -10 568 -10 374 0 581 3 577 10 -8 13 -1125
13 -1145 0z"/>
          <path d="M1370 3660 c0 -1087 3 -1640 10 -1640 7 0 10 553 10 1640 0 1087 -3
1640 -10 1640 -7 0 -10 -553 -10 -1640z"/>
          <path d="M3475 3640 c0 -910 1 -1283 2 -827 2 455 2 1199 0 1655 -1 455 -2 82
-2 -828z"/>

          <path d="M800 3860 c-9 -22 -11 -209 -6 -827 8 -1012 17 -1345 38 -1393 24
-56 53 -75 110 -75 46 0 53 3 85 40 19 22 42 54 50 70 9 17 46 76 84 132 78
117 99 155 99 179 0 10 8 26 18 37 16 18 17 75 20 849 3 931 7 873 -72 927
-22 15 -60 35 -84 44 -56 22 -200 47 -272 47 -54 0 -58 -2 -70 -30z m269 -51
c85 -22 126 -43 153 -80 19 -25 20 -50 18 -815 0 -644 -3 -800 -15 -843 -16
-60 -109 -232 -195 -361 -54 -81 -58 -85 -97 -88 -28 -2 -44 2 -53 14 -17 20
-16 -28 -25 1232 l-7 972 74 -6 c40 -4 106 -15 147 -25z"/>
          <path d="M1113 3207 c-67 -70 -177 -247 -153 -247 5 0 19 19 31 43 29 57 73
121 127 185 54 65 51 77 -5 19z"/>
          <path d="M1104 2908 c-67 -70 -129 -156 -120 -165 3 -4 8 -4 9 -2 2 2 23 31
47 64 24 33 68 84 98 113 31 29 49 52 40 52 -8 0 -42 -28 -74 -62z"/>
          <path d="M3845 3874 c-135 -25 -233 -81 -259 -149 -8 -22 -10 -256 -6 -855 l5
-825 47 -95 c26 -52 65 -120 88 -150 23 -30 55 -80 71 -111 30 -61 77 -114
110 -124 51 -17 106 13 140 75 19 36 28 366 35 1366 4 618 3 847 -5 863 -10
18 -20 21 -79 20 -37 0 -103 -7 -147 -15z m182 -896 c-6 -913 -17 -1234 -43
-1306 -17 -45 -52 -64 -83 -45 -44 28 -215 308 -249 410 l-27 78 5 804 5 803
35 30 c53 44 192 80 334 87 l29 1 -6 -862z"/>
          <path d="M3845 3126 c-74 -73 -124 -129 -120 -136 4 -6 22 8 46 37 21 26 79
84 129 127 84 74 103 96 81 96 -4 0 -66 -56 -136 -124z"/>
          <path d="M3892 2933 c-35 -17 -125 -111 -155 -162 -33 -57 0 -32 51 38 32 43
68 79 96 96 25 15 46 31 46 36 0 11 1 12 -38 -8z"/>
          <path d="M1923 1963 c269 -2 705 -2 970 0 265 1 46 2 -488 2 -533 0 -750 -1
-482 -2z"/>
          <path d="M1398 1929 c-62 -18 -120 -115 -181 -304 -63 -197 -80 -274 -81 -375
-1 -78 3 -103 21 -142 45 -98 156 -174 317 -217 77 -21 94 -22 950 -22 751 -1
881 1 942 15 141 30 245 84 310 159 92 109 84 268 -32 619 -45 134 -94 220
-139 241 -47 23 -152 40 -194 32 -40 -8 -1733 -8 -1821 -1 -30 3 -72 0 -92 -5z
m2016 -59 c38 -6 76 -16 84 -22 40 -34 100 -194 152 -408 32 -133 38 -226 18
-292 -18 -61 -79 -123 -161 -162 -137 -67 -134 -67 -1076 -66 -680 0 -859 3
-911 14 -150 32 -247 89 -297 174 -26 43 -28 55 -27 142 1 165 101 504 175
593 l30 37 972 0 c594 0 999 -4 1041 -10z"/>
          <path d="M2373 1746 c-139 -117 -589 -566 -567 -566 6 0 103 93 215 206 174
175 289 283 434 408 17 14 25 26 18 26 -7 0 -52 -33 -100 -74z"/>
          <path d="M2564 1695 c-113 -56 -202 -124 -333 -250 -154 -148 -412 -447 -399
-461 3 -2 59 59 124 138 234 282 452 476 626 561 78 38 105 57 79 57 -5 0 -49
-20 -97 -45z"/>
          <path d="M2813 1681 c-76 -51 -188 -146 -274 -231 -65 -64 -349 -369 -349
-374 0 -20 35 14 199 189 201 216 324 328 449 412 45 30 82 57 82 59 0 12 -28
-2 -107 -55z"/>
          <path d="M1814 1622 c-10 -20 -36 -54 -57 -75 -49 -49 -41 -60 10 -14 42 38
88 113 75 121 -4 2 -17 -12 -28 -32z"/>
          <path d="M2817 1349 c-49 -32 -107 -92 -107 -111 0 -6 20 9 44 34 24 25 64 58
90 73 25 15 46 29 46 31 0 12 -32 0 -73 -27z"/>
          <path d="M2122 823 c153 -2 403 -2 555 0 153 1 28 2 -277 2 -305 0 -430 -1
-278 -2z"/>
          <path d="M1630 710 c0 -7 258 -10 755 -10 497 0 755 3 755 10 0 7 -258 10
-755 10 -497 0 -755 -3 -755 -10z"/>
          <path d="M1089 5048 c-68 -81 -170 -238 -162 -251 3 -5 15 6 26 24 57 94 111
170 156 221 28 31 51 59 51 62 0 18 -24 -1 -71 -56z"/>
          <path d="M1109 4853 c-84 -86 -162 -183 -154 -192 4 -3 21 12 38 34 17 22 69
79 114 126 46 47 83 88 83 92 0 17 -20 2 -81 -60z"/>
          <path d="M3862 5332 c-16 -15 -55 -67 -85 -114 -31 -47 -61 -89 -66 -93 -6 -3
-11 -13 -11 -21 0 -26 21 -4 77 85 30 47 71 103 91 123 42 45 38 61 -6 20z"/>
          <path d="M3936 5173 c-70 -58 -114 -115 -121 -163 -6 -34 -5 -33 19 14 27 54
93 126 144 157 30 19 42 39 24 39 -4 0 -34 -21 -66 -47z"/>
        </g>
        {/* lock */}
        {action === "lock" &&
          <g transform="translate(18,52) scale(0.7,0.7)">
            <path fill={color} stroke="none" d="M4 8V6a6 6 0 1 1 12 0v2h1a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2h1zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0zM7 6v2h6V6a3 3 0 0 0-6 0z" />
          </g>
        }
        {action === "unlock" &&
          <g transform="translate(18,52) scale(0.7,0.7)">
            <path fill={color} stroke="none" d="M4 8V6a6 6 0 1 1 12 0h-3v2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-8c0-1.1.9-2 2-2h1zm5 6.73V17h2v-2.27a2 2 0 1 0-2 0zM7 6v2h6V6a3 3 0 0 0-6 0z" />
          </g>
        }
        {status && status.sta === 1 &&
          <g transform='translate(19,12) scale(0.77,0.77)'>
            <path fill={color} stroke="none" d="M10 3c0 1.313-.304 2.508-.8 3.4a1.991 1.991 0 0 0-1.484-.38c-.28-.982-.91-2.04-1.838-2.969a8.368 8.368 0 0 0-.491-.454A5.976 5.976 0 0 1 8 2c.691 0 1.355.117 1.973.332c.018.219.027.442.027.668Zm0 5c0 .073-.004.146-.012.217c1.018-.019 2.2-.353 3.331-1.006a8.39 8.39 0 0 0 .57-.361a6.004 6.004 0 0 0-2.53-3.823a9.02 9.02 0 0 1-.145.64c-.34 1.269-.944 2.346-1.656 3.079c.277.343.442.78.442 1.254Zm-.137.728a2.007 2.007 0 0 1-1.07 1.109c.525.87 1.405 1.725 2.535 2.377c.2.116.402.222.605.317a5.986 5.986 0 0 0 2.053-4.111c-.208.073-.421.14-.641.199c-1.264.339-2.493.356-3.482.11ZM8 10c-.45 0-.866-.149-1.2-.4c-.494.89-.796 2.082-.796 3.391c0 .23.01.457.027.678A5.99 5.99 0 0 0 8 14c.94 0 1.83-.216 2.623-.602a8.359 8.359 0 0 1-.497-.458c-.925-.926-1.555-1.981-1.836-2.96c-.094.013-.191.02-.29.02ZM6 8c0-.08.005-.16.014-.239c-1.02.017-2.205.351-3.34 1.007a8.366 8.366 0 0 0-.568.359a6.003 6.003 0 0 0 2.525 3.839a8.37 8.37 0 0 1 .148-.653c.34-1.267.94-2.342 1.65-3.075A1.988 1.988 0 0 1 6 8Zm-3.347-.632c1.267-.34 2.498-.355 3.488-.107c.196-.494.583-.89 1.07-1.1c-.524-.874-1.406-1.733-2.541-2.388a8.363 8.363 0 0 0-.594-.312a5.987 5.987 0 0 0-2.06 4.106c.206-.074.418-.14.637-.199ZM8 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z" />
            <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="360 8 8" to="0 8 8" dur="1.5s" additive="sum" repeatCount="indefinite" />
          </g>
        }

        {action === "turnon" &&
          <g transform="translate(18,52) scale(0.9,0.9)">
            <path fill={color} stroke="none" d="M10 3c0 1.313-.304 2.508-.8 3.4a1.991 1.991 0 0 0-1.484-.38c-.28-.982-.91-2.04-1.838-2.969a8.368 8.368 0 0 0-.491-.454A5.976 5.976 0 0 1 8 2c.691 0 1.355.117 1.973.332c.018.219.027.442.027.668Zm0 5c0 .073-.004.146-.012.217c1.018-.019 2.2-.353 3.331-1.006a8.39 8.39 0 0 0 .57-.361a6.004 6.004 0 0 0-2.53-3.823a9.02 9.02 0 0 1-.145.64c-.34 1.269-.944 2.346-1.656 3.079c.277.343.442.78.442 1.254Zm-.137.728a2.007 2.007 0 0 1-1.07 1.109c.525.87 1.405 1.725 2.535 2.377c.2.116.402.222.605.317a5.986 5.986 0 0 0 2.053-4.111c-.208.073-.421.14-.641.199c-1.264.339-2.493.356-3.482.11ZM8 10c-.45 0-.866-.149-1.2-.4c-.494.89-.796 2.082-.796 3.391c0 .23.01.457.027.678A5.99 5.99 0 0 0 8 14c.94 0 1.83-.216 2.623-.602a8.359 8.359 0 0 1-.497-.458c-.925-.926-1.555-1.981-1.836-2.96c-.094.013-.191.02-.29.02ZM6 8c0-.08.005-.16.014-.239c-1.02.017-2.205.351-3.34 1.007a8.366 8.366 0 0 0-.568.359a6.003 6.003 0 0 0 2.525 3.839a8.37 8.37 0 0 1 .148-.653c.34-1.267.94-2.342 1.65-3.075A1.988 1.988 0 0 1 6 8Zm-3.347-.632c1.267-.34 2.498-.355 3.488-.107c.196-.494.583-.89 1.07-1.1c-.524-.874-1.406-1.733-2.541-2.388a8.363 8.363 0 0 0-.594-.312a5.987 5.987 0 0 0-2.06 4.106c.206-.074.418-.14.637-.199ZM8 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z" />
            <path fill={color} stroke="none" d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14Zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16Z" />
          </g>
        }
        {action === "turnoff" &&
          <g transform="translate(18,52) scale(0.6,0.6)">
            <path fill={color} stroke="none" d="M10 11c-.57 0-1 .45-1 1s.43 1 1 1c.54 0 1-.45 1-1s-.46-1-1-1m.5-9c4.5 0 4.59 3.57 2.23 4.75c-.99.49-1.43 1.54-1.62 2.47c.48.2.89.51 1.22.91c3.7-2 7.67-1.21 7.67 2.37c0 4.5-3.57 4.6-4.74 2.23c-.5-.99-1.56-1.43-2.49-1.62c-.2.48-.51.89-.91 1.23C13.85 18.03 13.06 22 9.5 22c-4.5 0-4.6-3.58-2.24-4.76c.98-.49 1.42-1.53 1.62-2.45c-.49-.2-.92-.52-1.24-.92C3.95 15.85 0 15.07 0 11.5C0 7 3.56 6.89 4.73 9.26c.5.99 1.55 1.42 2.48 1.61c.19-.48.51-.9.92-1.22C6.14 5.96 6.93 2 10.5 2M22 13V7h2v6h-2m0 4v-2h2v2h-2Z" />

          </g>
        }
        {(status && status.sta === 1) && <>
          <g transform="translate(50,17) rotate(180)">
            <g id="lightLFront" transform="translate(-25,20) scale(0.1,-0.1)">
              <path fill="url(#radial-gradient)" d="M316.4176,229.6298c.6467-5.8978-.6651-12.6523,4.279-16.1727,2.3753-1.6913,5.3736-3.0153,7.3419-5.2137,2.5389-2.8358,.0261-5.5517,.1972-9.2017,.5802-12.374-6.4191-8.439,1.056-19.9663,5.4337-8.3793,17.9971-29.3277,25.9032-35.205,9.0036-6.6932,17.6733-15.3859,27.2801-21.1709,10.2446-6.1691,20.1678-10.903,32.1156-12.9835,12.5969-2.1935,25.203-4.9131,37.9951-4.9217,11.5909-.0078,17.1414,1.397,25.1891,.8953,7.6017-.4739,15.8243-.0011,23.513-.1068C487.7724,45.1595,433.828,0,369.3287,0c-74.6839,0-111.3876,61.1686-111.3876,135.8525,0,55.7131,10.8104,86.2502,58.931,106.9615-1.1288-10.6745-1.6337-2.4307-.4545-13.1843Z" />
              <path fill="url(#linear-gradient)" d="M358.1847,143.009c.553-3.5095,3.4412-4.2135,16.288-14.4053,5.1419-4.0792,10.085-6.8348,15.5505-10.1127,6.9996-4.1979,12.3502-5.5554,14.387-3.2892,1.9663,2.1878,5.5531,4.8349,2.6487,7.3048-3.8824,3.3015-13.4053,4.3977-27.4443,13.3459-10.5936,6.7521-12.931,11.4362-17.1982,12.5561-2.6551,.6968-4.7257-2.2642-4.2316-5.3996Z" />
            </g>
            <g id="lightRFront" transform="translate(-22,20) scale(0.1,-0.1)">
              <path fill='url(#radial-gradient-2)' d="M669.015,230.1771c-.6467-5.8978,.6651-12.6523-4.279-16.1727-2.3753-1.6913-5.3736-3.0153-7.3419-5.2137-2.5389-2.8358-.0261-5.5517-.1972-9.2017-.5802-12.374,6.4191-8.439-1.056-19.9663-5.4337-8.3793-17.9971-29.3277-25.9032-35.205-9.0036-6.6932-17.6733-15.3859-27.2801-21.1709-10.2446-6.1691-20.1678-10.903-32.1156-12.9835-12.5969-2.1935-25.203-4.9131-37.9951-4.9217-11.5909-.0078-17.1414,1.397-25.1891,.8953-7.6017-.4739-15.8243-.0011-23.513-.1068C497.6602,45.7068,551.6046,.5473,616.1039,.5473c74.6839,0,111.3876,61.1686,111.3876,135.8525,0,55.7131-10.8104,86.2502-58.931,106.9615,1.1288-10.6745,1.6337-2.4307,.4545-13.1843Z" />
              <path fill='url(#linear-gradient-2)' d="M627.248,143.5563c-.553-3.5095-3.4412-4.2135-16.288-14.4053-5.1419-4.0792-10.085-6.8348-15.5505-10.1127-6.9996-4.1979-12.3502-5.5554-14.387-3.2892-1.9663,2.1878-5.5531,4.8349-2.6487,7.3048,3.8824,3.3015,13.4053,4.3977,27.4443,13.3459,10.5936,6.7521,12.931,11.4362,17.1982,12.5561,2.6551,.6968,4.7257-2.2642,4.2316-5.3996Z" />
            </g>
          </g>
        </>
        }
        {status && (status?.sta === 1 || status?.light === 1) && <>
          <g transform="translate(-24.3,4.6) scale(0.1,0.1)">
            <path id="bkLightL" fill="url(#radial-gradient-3)" d="M404.0588,921.5121c-3.5544-.2236-4.7198-2.9059-11.1021-4.1171-5.2354-.9935-6.4325-1.4736-11.487-2.9169-5.7562-1.6436-12.0823-2.8869-17.8182-4.691-6.0869-1.9145-9.0395-4.2905-13.4402-9.0349-4.2961-4.6316-6.3592-7.9102-10.6231-13.2402-3.2941-4.1176,3.7079-3.4238-1.0797-2.3699-3.969,.8737-5.8535,17.0506-8.685,21.4546,1.546-2.4045-10.815,31.7388,15.5294,54.3271,25.214,21.619,24.4967,12.9501,43.4068,3.2941,3.0885-4.4979,11.3739-18.7617,12.9461-24,.8826-2.9405,1.883-6.5739,1.7647-9.6471-.0776-2.0149,.6441-4.1113,.5882-6.1176" />
            <path id="bkLightL-2" data-name="bkLightL" fill="url(#radial-gradient-4)" d="M580.3322,921.5077c3.5544-.2236,4.7198-2.9059,11.1021-4.1171,5.2354-.9935,6.4325-1.4736,11.487-2.9169,5.7562-1.6436,12.0823-2.8869,17.8182-4.691,6.0869-1.9145,9.0395-4.2905,13.4402-9.0349,4.2961-4.6316,6.3592-7.9102,10.6231-13.2402,3.2941-4.1176-3.7079-3.4238,1.0797-2.3699,3.969,.8737,5.8535,17.0506,8.685,21.4546-1.546-2.4045,10.815,31.7388-15.5294,54.3271-25.214,21.619-24.4967,12.9501-43.4068,3.2941-3.0885-4.4979-11.3739-18.7617-12.9461-24-.8826-2.9405-1.883-6.5739-1.7647-9.6471,.0776-2.0149-.6441-4.1113-.5882-6.1176" />
          </g>

        </>}
      </svg>
      {title !== '' ? title :
        (device && device.isDefault && `${t('home.device_name')}:${device.deviceName}` || `${t('home.unknown')}`)}

    </Box >
  );
  return carTopSvg;
}