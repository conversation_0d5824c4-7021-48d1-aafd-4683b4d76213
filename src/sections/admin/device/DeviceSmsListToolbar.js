import PropTypes from 'prop-types';
// @mui
import { useTheme, styled } from '@mui/material/styles';
import { Toolbar, Tooltip, IconButton, Typography, InputAdornment, Box, TextField, Button } from '@mui/material';
// components
import Iconify from '../../../components/Iconify';
import InputStyle from '../../../components/InputStyle';
import { IconButtonAnimate } from '../../../components/animate';

// ----------------------------------------------------------------------

const RootStyle = styled(Toolbar)(({ theme }) => ({
  height: 70,
  display: 'flex',
  justifyContent: 'center',
  gap:16,
}));

// ----------------------------------------------------------------------

DeviceSmsListToolbar.propTypes = {
  filterName: PropTypes.string,
  onFilterName: PropTypes.func,
  onFilterFrom: PropTypes.func,
  onFilterTo: PropTypes.func,
};

export default function DeviceSmsListToolbar({ filterName, onFilterName, from, onFilterFrom, to, onFilterTo,onChangeDate }) {
  return (
    <RootStyle  sx={{ "& .MuiOutlinedInput-root": { width: 120 } }}>
      <InputStyle
       
        value={from}
        size="small"
        onChange={(event) => onFilterFrom(event.target.value)}
        placeholder="From..."
        
      />
      
      <InputStyle
     
        value={to}
        size="small"
        onChange={(event) => onFilterTo(event.target.value)}
        placeholder="To..."
        
      />
      <IconButtonAnimate onClick = {onChangeDate}><Iconify  icon = 'ei:refresh' /></IconButtonAnimate> 

    </RootStyle >
  );
}
