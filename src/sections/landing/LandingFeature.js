import { Icon } from "@iconify/react";
import { Grid, Stack, styled, Typography } from "@mui/material";

const RootStyle = styled(Stack)(({ theme }) => ({
    position: 'relative',
    display: 'flex',
    // height: '80vh',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 16,
    backgroundColor: '#ff2f1f20',
    padding: 8,
    minHeight: 160,
    marginTop: '-80px',
    marginBottom: '80px',
}));

export default function LandingFeature() {
    return (<Stack justifyContent={'center'}
        alignItems={'center'} >


        <RootStyle sx={
            { width: { xs: '90%', sm: '80%', md: '70%' } }} >
            <Grid container spacing={3} >
                <Grid item xs={12}
                    sm={3} >
                    <Stack gap={1}
                        justifyContent={'center'}
                        alignItems={'center'} >
                        <Icon icon="material-symbols:nest-remote-comfort-sensor-outline"
                            width={64}
                        /> <Typography variant="h4" > Апп асаалт </Typography> </Stack> </Grid> <Grid item xs={12}
                            sm={3} >
                    <Stack gap={1}
                        justifyContent={'center'}
                        alignItems={'center'} >
                       <Icon icon="mdi:message" width={64} /> <Typography variant="h4" > Мессеж асаалт  </Typography> </Stack> </Grid> <Grid item xs={12}
                            sm={3} >
                    <Stack gap={1}
                        justifyContent={'center'}
                        alignItems={'center'} >
                        <Icon icon="mdi:key-wireless"
                            width={64}
                        /> <Typography variant="h4" > Түлхүүр асаалт </Typography> </Stack> </Grid> <Grid item xs={12}
                            sm={3} >
                    <Stack gap={1}
                        justifyContent={'center'}
                        alignItems={'center'} >
                        <Icon icon="mdi:chat"
                            width={64}
                        /> <Typography variant="h4" > Messenger асаалт </Typography> </Stack> </Grid> </Grid> </RootStyle> </Stack>
    )
}