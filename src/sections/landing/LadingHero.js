import { Box, Stack, styled, Typography } from "@mui/material";
import Image from "../../components/Image";

const RootStyle = styled(Stack)(({ theme }) => ({
    position: 'relative',
    display: 'flex',
    height: '700px',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    width: '100%',
    backgroundImage: 'url(/images/bg.jpg)'
}));
export default function LandingHero() {
    return (
        <RootStyle>
            <Stack
                height="100%"
                width={'100%'}
                direction={{ xs: 'column', sm: 'column', md: 'row' }}
                alignItems={'center'}
                justifyContent={'center'}
                sx={{ backdropFilter: 'blur(4px)' }}
                marginTop={8}
                paddingX={{ xs: 2, sm: 4, md: 6 }}
            >
                <Stack padding={4} sx={{ backdropFilter: 'blur(2px)' }} justifyContent={'flex-start'}>
                    <Typography variant="h5" sx={{ mb: 2 }}>
                        {/* {t('lading.hero-1-title')} */}
                        Машинаа утсандаа багтаа
                    </Typography>
                    <Typography color="orange" variant="h2" sx={{ mb: 2 }}>
                        {/* {t('lading.hero-1-label')} */}
                        Машины хяналтын систем
                    </Typography>
                    <Typography variant="h5" sx={{ mb: 4 }} color={'text.secondary'}>
                        {/* {t('lading.hero-1-description')} */}
                        Алсын асаалт, Байршил тогтоогч, хянах систем, дохиолол...
                    </Typography>
                    <Typography variant="h6" sx={{ display: { xs: 'block', md: 'none' }, mt: 2 }} color={'text.secondary'}>
                        утас: 77372929
                    </Typography>

                </Stack>
                <Stack padding={4} sx={{ backdropFilter: 'blur(2px)' }} mb={8}>
                    <Box sx={{ maxWidth: { xs: 340, sm: 400, md: 500, lg: 600 } }}>
                        <Image src="/images/car-1.gif" alt="Car Animation"></Image>
                    </Box>
                </Stack>
            </Stack>
        </RootStyle>
    )
}
