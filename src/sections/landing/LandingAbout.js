import { Icon } from "@iconify/react";
import {  Grid, Stack, Typography,Container } from "@mui/material";
import { useTranslation } from "react-i18next";
import {} from "../../components/animate";

export default function LandingAbout() {
    const { t } = useTranslation();
    return (
        <Container sx ={{mb:4}}>
            <Stack >


                <Grid container spacing={4}>
                    <Grid item xs={12} sm={12} md={6}>
                        <Stack sx={{ backgroundImage: 'url(/images/map-bg.png)', backgroundRepeat: 'no-repeat' }} padding={6} borderRadius={2}>
                                <Stack gap={2} >
                                    <Typography variant="subtitle1">{t('Бидний тухай')}</Typography>
                                    <Typography variant="h4" color={'orange'}>{t('Бид үйлчилгээний соёлыг дараагийн түвшинд....')}</Typography>
                                    <Typography>
                                        {t("Бид техник хангамж болон програм хангамжийн чиглэлээр үйл ажиллагаа явуулдаг монголын цөөн компаниудын нэг билээ. Бид үргэлж шинийг эрэлхийлж шинийг санаачилан хэрэглэгчидийнхээ тав тух, цаг завыг хөнгөвчлөх бүтээгдхүүнийг гаргахын төлөө ажилдаг")}

                                    </Typography>
                                </Stack>
                        </Stack>
                    </Grid>

                    <Grid item xs={12} sm={12} md={6}>
                        <Stack sx={{ backgroundColor: 'orange' }} padding={6} borderRadius={2}>
                                <Stack gap={2} >
                                    <Typography variant="subtitle1">{t('Яагаад бид гэж?')}</Typography>
                                    {/* <Typography variant="h4">{t('!')}</Typography> */}
                                    <Typography>
                                        {t("Бид үйлчилгээгээ тасралтгүй сайжруулдаг")}
                                    </Typography>
                                    <Stack gap={2}>
                                        <Stack direction={'row'} gap={4} alignItems={'center'}>
                                            <Icon icon="mdi:diamond-stone" width={64} />
                                            <Stack gap={1}>
                                                <Typography variant="h6">{t('Найдвартай түнш')}</Typography>
                                                <Typography>{t('Бид үргэж шинийг санаачилдаг')}</Typography>
                                            </Stack>
                                        </Stack>

                                        <Stack direction={'row'} gap={4} alignItems={'center'}>
                                            <Icon icon="material-symbols:directions-car-outline-rounded" width={64} />
                                            <Stack gap={1}>
                                                <Typography variant="h6">{t('Шуурхай үйлчилгээ')}</Typography>
                                                <Typography>{t('Чанарын баталгаа өгдөг')}</Typography>
                                            </Stack>
                                        </Stack>
                                        <Stack direction={'row'} gap={4} alignItems={'center'}>
                                            <Icon icon="ri:dashboard-3-line" width={64} />
                                            <Stack gap={1}>
                                                <Typography variant="h6">{t('Цаг бол алт')}</Typography>
                                                <Typography>{t('Хямд үнэ эдийн засгийн хэмнэлт')}</Typography>
                                            </Stack>
                                        </Stack>
                                    </Stack>
                                </Stack>
                        </Stack>
                    </Grid>

                </Grid>
            </Stack>
        </Container>
    )
}
