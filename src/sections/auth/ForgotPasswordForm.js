import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
// form
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import { Stack, Alert } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useNavigate } from 'react-router-dom';

// components
import { FormProvider, RHFTextField } from '../../components/hook-form';
import axios from '../../utils/axios';

// ----------------------------------------------------------------------

export default function ForgotPasswordForm() {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const LoginSchema = Yup.object().shape({
    phoneNumber: Yup.number().min(99999, 'Phone number cannot be less than 6 digits').max(**********, 'Phone number cannot be greater than 10 digits').required('Phone number is required'),
    pinCode: Yup.string().min(6, 'Pin code must be at least 6 characters').required('New pin code is required'),
    deviceNumber: Yup.string().length(6, 'Device number must be exactly 6 digits').required('Last 6 digits of the device number are required'),
  });

  const defaultValues = {
    phoneNumber: '',
    pinCode: '',
    deviceNumber: '',
  };

  const methods = useForm({
    resolver: yupResolver(LoginSchema),
    defaultValues,
  });

  const {
 
    handleSubmit,
    formState: { errors, isSubmitting },
  } = methods;

  const onResetPassword = async (data) => {
    try {
      const response = await axios.post('/api/auth/reset-password', data);

      if (response.status === 200 && response.data.success) {
        enqueueSnackbar(response?.data?.message, { variant: 'success' });
        navigate('/'); // Redirect after successful reset
      } else {
        enqueueSnackbar(response?.data?.message, { variant: 'error' });
      }
    } catch (err) {
      enqueueSnackbar('Error resetting password. Please try again.', { variant: 'error' });
    }
  };

  return (
    <>
      <FormProvider methods={methods} onSubmit={handleSubmit(onResetPassword)}>
        <Stack spacing={3}>
          {!!errors.afterSubmit && <Alert severity="error"> {errors.afterSubmit.message} </Alert>}
          
          {/* Phone Number Field */}
          <RHFTextField name="phoneNumber" label="Phone Number" />

          {/* New Pin Code Field */}
          <RHFTextField name="pinCode" label="New Pin Code" />

          {/* Device Number Field (last 6 digits) */}
          <RHFTextField name="deviceNumber" label="Last 6 Digits of Device Number" />

          {/* Submit Button */}
          <LoadingButton
            fullWidth
            size="large"
            sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
            type="submit"
            variant="contained"
            loading={isSubmitting}
          >
            Reset Password
          </LoadingButton>
        </Stack>
      </FormProvider>
    </>
  );
}