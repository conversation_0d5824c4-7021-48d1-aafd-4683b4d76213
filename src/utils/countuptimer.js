import React, { useState, useEffect, useRef } from 'react';
import '../utils/countUp.css'

export default function CountUp({ start, onEvent }) {
  const [time, setTime] = useState('');
  const seconds = useRef(start);
  // const [newDevice, setNewDevice] = useState({ deviceNumber: "", type: "4g", uix: "Car", _id: '', isDefault: false,});

  useEffect(() => {
    let interval, eventInterval;
    onEvent(seconds.current === 0 ? 1 : seconds.current);
    interval = setInterval(() => {

      seconds.current++;
      // localStorage.setItem('seconds', seconds.current);
      const days = String(Math.floor(seconds.current / 3600 / 24)).padStart(2, '0');
      const hours = String(Math.floor(seconds.current / 3600)).padStart(2, '0');
      const minutes = String(Math.floor((seconds.current % 3600) / 60)).padStart(2, '0');
      const formattedSeconds = String(seconds.current % 60).padStart(2, '0');
      const formattedTime = `${days}:${hours}:${minutes}:${formattedSeconds}`;
      setTime(formattedTime);
      if (formattedSeconds === "01") {
        onEvent(seconds.current);
      }
    }, 1000);
    return () => clearInterval(interval);
  }, [start]);







  return (
    <div className='clock'>
      <div className='display'>
        {time}
      </div>
    </div>
  );

}
