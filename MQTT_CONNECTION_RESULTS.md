# MQTT Connection Test Results

## TCP (Port 1883) Tests

| Broker IP | Port | Protocol | Status | Notes |
|-----------|------|----------|--------|-------|
| ************* | 1883 | MQTT (TCP) | ✅ Success | Successfully connected, published, and received messages |
| ************ | 1883 | MQTT (TCP) | ✅ Success | Successfully connected, published, and received messages |
| ************** | 1883 | MQTT (TCP) | ✅ Success | Successfully connected, published, and received messages |

## WebSocket Tests

| Broker IP | Port | Protocol | Status | Notes |
|-----------|------|----------|--------|-------|
| ************* | 8084 | WS | ✅ Success | Successfully connected, published, and received messages |
| ************* | 8083 | WS | ✅ Success | Successfully connected, published, and received messages |
| ************ | 8084 | WS | ❌ Failed | Connection error: "socket hang up" |
| ************ | 8083 | WS | ✅ Success | Successfully connected, published, and received messages |
| ************** | 8084 | WS | ❌ Failed | Connection error: "connack timeout" |
| ************** | 8083 | WS | ✅ Success | Successfully connected, published, and received messages |

## Recommended Configurations

Based on the test results, the following configurations are recommended:

### For Node.js Applications (Server-side)

```javascript
// MQTT over TCP (Port 1883)
const options = {
  clientId: `aslaacv_${Math.random().toString(16).substring(2, 10)}`,
  clean: true,
  connectTimeout: 4000,
  reconnectPeriod: 1000,
  port: 1883,
  protocol: 'mqtt',
};

// Any of these brokers work well
const brokerUrl = 'mqtt://*************'; // Primary
// const brokerUrl = 'mqtt://************'; // Backup 1
// const brokerUrl = 'mqtt://**************'; // Backup 2

const client = mqtt.connect(brokerUrl, options);
```

### For Browser Applications (Client-side)

```javascript
// MQTT over WebSocket (Port 8083)
const options = {
  clientId: `aslaacv_${Math.random().toString(16).substring(2, 10)}`,
  clean: true,
  connectTimeout: 4000,
  reconnectPeriod: 1000,
  keepalive: 60,
  protocolId: 'MQTT',
  protocolVersion: 4,
  rejectUnauthorized: false,
};

// All three brokers work on port 8083 with WebSocket
// Primary broker configuration
const brokerUrl = 'ws://*************:8083/mqtt';
// Fallback options:
// const brokerUrl = 'ws://************:8083/mqtt';
// const brokerUrl = 'ws://**************:8083/mqtt';

const client = mqtt.connect(brokerUrl, options);
```

## Fallback Strategy

For robust MQTT connectivity, implement a fallback strategy:

1. Try the primary broker first (*************)
2. If connection fails, try the backup brokers in sequence
3. For browser applications, use WebSocket on port 8083 (works with all tested IPs)
4. For Node.js applications, TCP (1883) is more reliable than WebSocket

## Connection Monitoring

To ensure reliable MQTT communication:

1. Implement connection monitoring with automatic reconnection
2. Add error handling for different types of connection failures
3. Use QoS 1 for important messages to ensure delivery
4. Implement message buffering for offline scenarios
