<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Iframe Client</title>
    <script src="https://unpkg.com/mqtt/dist/mqtt.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        .status {
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        .log {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .log-entry {
            margin-bottom: 4px;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.success {
            color: #28a745;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.disconnect {
            background-color: #dc3545;
        }
        button.disconnect:hover {
            background-color: #c82333;
        }
        .device-info {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .device-info div {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="status" class="status disconnected">Disconnected</div>

        <div class="device-info">
            <div id="device-number">Device: <span>-</span></div>
            <div id="firmware-version">Firmware: <span>-</span></div>
            <div id="sound-enabled">Sound: <span>-</span></div>
            <div id="key-enabled">Key: <span>-</span></div>
        </div>

        <div>
            <button id="connect-1">Connect to *************</button>
            <button id="connect-2">Connect to ************</button>
            <button id="connect-3">Connect to **************</button>
            <button id="disconnect" class="disconnect">Disconnect</button>
            <button id="check">Send Check</button>
            <button id="clear-log">Clear Log</button>
        </div>

        <div id="log" class="log"></div>
    </div>

    <script>
        // Get device number from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const deviceNumber = urlParams.get('device') || '123456';
        document.getElementById('device-number').querySelector('span').textContent = deviceNumber;

        // Log function
        const logElement = document.getElementById('log');
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;

            // Send log to parent window
            try {
                window.parent.postMessage({
                    type: 'mqtt-log',
                    message: message,
                    messageType: type
                }, '*');
            } catch (e) {
                // console.error('Error sending message to parent:', e);
            }
        }

        // Clear log button
        document.getElementById('clear-log').addEventListener('click', () => {
            logElement.innerHTML = '';
        });

        // MQTT client
        let client = null;
        let activeBroker = null;

        // Connect buttons
        document.getElementById('connect-1').addEventListener('click', () => connect('*************'));
        document.getElementById('connect-2').addEventListener('click', () => connect('************'));
        document.getElementById('connect-3').addEventListener('click', () => connect('**************'));

        // Disconnect button
        document.getElementById('disconnect').addEventListener('click', disconnect);

        // Check button
        document.getElementById('check').addEventListener('click', sendCheckCommand);

        // Connect function
        function connect(broker) {
            try {
                // Update status
                const statusElement = document.getElementById('status');
                statusElement.className = 'status connecting';
                statusElement.textContent = `Connecting to ${broker}...`;

                // Disconnect existing client if any
                if (client) {
                    disconnect();
                }

                // Create client ID
                const clientId = `iframe_${deviceNumber}_${Math.random().toString(16).substring(2, 10)}`;

                // Create broker URL
                const brokerUrl = `ws://${broker}:8083/mqtt`;

                log(`Connecting to ${brokerUrl} with client ID ${clientId}`);

                // Connection options
                const options = {
                    clientId: clientId,
                    username: "admin",
                    password: "public",
                    clean: true,
                    keepalive: 60,
                    reconnectPeriod: 1000,
                    connectTimeout: 30000
                };

                // Create client
                client = mqtt.connect(brokerUrl, options);
                activeBroker = broker;

                // Set up event handlers
                client.on('connect', () => {
                    log(`Connected to ${broker}!`, 'success');
                    statusElement.className = 'status connected';
                    statusElement.textContent = `Connected to ${broker}`;

                    // Send connected message to parent window
                    try {
                        window.parent.postMessage({
                            type: 'mqtt-connected',
                            broker: broker
                        }, '*');
                    } catch (e) {
                        console.error('Error sending message to parent:', e);
                    }

                    // Subscribe to device topics
                    client.subscribe(`${deviceNumber}/msg`);
                    client.subscribe(deviceNumber);
                    log(`Subscribed to ${deviceNumber}/msg and ${deviceNumber}`);

                    // Send check command
                    sendCheckCommand();
                });

                client.on('message', (topic, message) => {
                    try {
                        const msg = message.toString();
                        log(`Received message on ${topic}: ${msg}`);

                        // Try to parse as JSON
                        try {
                            const payload = JSON.parse(msg);

                            // Send message to parent window
                            try {
                                window.parent.postMessage({
                                    type: 'mqtt-message',
                                    topic: topic,
                                    payload: payload
                                }, '*');
                            } catch (e) {
                                console.error('Error sending message to parent:', e);
                            }

                            // Update device info
                            if (payload.firmware) {
                                document.getElementById('firmware-version').querySelector('span').textContent = payload.firmware;
                            }

                            if (payload.sound !== undefined) {
                                document.getElementById('sound-enabled').querySelector('span').textContent =
                                    payload.sound === 'on' || payload.sound === true ? 'Enabled' : 'Disabled';
                            }

                            if (payload.key !== undefined) {
                                document.getElementById('key-enabled').querySelector('span').textContent =
                                    payload.key === 'on' || payload.key === true ? 'Enabled' : 'Disabled';
                            }
                        } catch (e) {
                            // Not JSON, that's fine
                            log('Message is not valid JSON');
                        }
                    } catch (error) {
                        log(`Error processing message: ${error.message}`, 'error');
                    }
                });

                client.on('error', (error) => {
                    log(`Error: ${error.message}`, 'error');
                    statusElement.className = 'status disconnected';
                    statusElement.textContent = `Error: ${error.message}`;

                    // Send error message to parent window
                    try {
                        window.parent.postMessage({
                            type: 'mqtt-error',
                            message: error.message,
                            broker: broker
                        }, '*');
                    } catch (e) {
                        // console.error('Error sending message to parent:', e);
                    }
                });

                client.on('offline', () => {
                    log(`Client is offline`, 'error');
                    statusElement.className = 'status disconnected';
                    statusElement.textContent = 'Offline';

                    // Send offline message to parent window
                    try {
                        window.parent.postMessage({
                            type: 'mqtt-offline',
                            broker: broker
                        }, '*');
                    } catch (e) {
                        console.error('Error sending message to parent:', e);
                    }
                });

                client.on('reconnect', () => {
                    log(`Reconnecting to ${broker}...`);
                    statusElement.className = 'status connecting';
                    statusElement.textContent = `Reconnecting to ${broker}...`;
                });

                return true;
            } catch (error) {
                log(`Error creating client: ${error.message}`, 'error');
                return false;
            }
        }

        // Disconnect function
        function disconnect() {
            if (client) {
                client.end();
                client = null;
                activeBroker = null;

                const statusElement = document.getElementById('status');
                statusElement.className = 'status disconnected';
                statusElement.textContent = 'Disconnected';

                log('Disconnected from broker');

                // Send disconnected message to parent window
                try {
                    window.parent.postMessage({
                        type: 'mqtt-disconnected'
                    }, '*');
                } catch (e) {
                    console.error('Error sending message to parent:', e);
                }
            }
        }

        // Send check command
        function sendCheckCommand() {
            if (!client) {
                log('Cannot send check command, not connected', 'error');
                return;
            }

            try {
                const checkCommand = {
                    cmd: "check",
                    device: deviceNumber,
                    timestamp: new Date().toISOString()
                };

                const topic = `${deviceNumber}/cmd`;
                const message = JSON.stringify(checkCommand);

                log(`Sending check command to ${topic}: ${message}`);
                client.publish(topic, message);
            } catch (error) {
                log(`Error sending check command: ${error.message}`, 'error');
            }
        }

        // Listen for messages from parent window
        window.addEventListener('message', (event) => {
            try {
                const message = event.data;

                if (message.type === 'mqtt-connect') {
                    connect(message.broker || '*************');
                } else if (message.type === 'mqtt-disconnect') {
                    disconnect();
                } else if (message.type === 'mqtt-check') {
                    sendCheckCommand();
                } else if (message.type === 'mqtt-publish') {
                    if (client && message.topic && message.payload) {
                        client.publish(message.topic, JSON.stringify(message.payload));
                        log(`Published to ${message.topic}: ${JSON.stringify(message.payload)}`);
                    }
                }
            } catch (e) {
                log(`Error processing message from parent: ${e.message}`, 'error');
            }
        });

        // Auto-connect to first broker on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                connect('*************');
            }, 500);
        });
    </script>
</body>
</html>
