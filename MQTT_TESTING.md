# MQTT Testing Guide

This guide provides instructions for testing MQTT connections using both browser-based and Node.js approaches.

## Browser-Based Testing (WebSocket)

The React application includes several MQTT test components that use WebSockets to connect to MQTT brokers:

1. Navigate to `/mqtt-test` in the application
2. Try the different test tabs:
   - **Simple WebSocket Test**: Basic WebSocket connection to EMQX broker
   - **Direct URL Test**: Customizable broker URL for WebSocket connections
   - **Advanced WebSocket Test**: More features for testing MQTT over WebSockets
   - **HTTP API Test**: Uses HTTP API instead of WebSockets (fallback option)

## Node.js Testing (TCP/Port 1883)

For testing MQTT over the standard TCP port 1883, use the provided Node.js scripts:

### Basic EMQX Broker Test

```bash
node mqtt-tcp-test.js
```

This script connects to the public EMQX broker at `broker.emqx.io:1883`, subscribes to the `aslaa/test` topic, publishes a test message, and then disconnects after 10 seconds.

### Local Broker Test

```bash
node mqtt-local-test.js
```

This script connects to a local MQTT broker at `localhost:1883`. Make sure you have a local MQTT broker (like <PERSON><PERSON><PERSON><PERSON>, EMQX, etc.) running before using this script.

### Interactive CLI Test

```bash
node mqtt-cli-test.js [broker-url] [topic]
```

This script provides an interactive command-line interface for testing MQTT connections. You can:
- Specify a custom broker URL and topic
- Send messages by typing them and pressing Enter
- Receive messages published to the subscribed topic
- Exit by typing "exit"

Examples:
```bash
# Connect to the default EMQX broker and topic
node mqtt-cli-test.js

# Connect to a custom broker and topic
node mqtt-cli-test.js mqtt://localhost:1883 my/custom/topic

# Connect to a secure broker
node mqtt-cli-test.js mqtts://broker.example.com:8883 secure/topic
```

## MQTT Service in React Application

The application includes two MQTT service implementations:

1. **WebSocket-based service** (`src/services/mqtt.js`):
   - Uses WebSockets (ws/wss) for browser compatibility
   - Suitable for browser environments

2. **TCP-based service** (`src/services/mqttTcpService.js`):
   - Uses TCP connection (port 1883)
   - Only works in Node.js environments, not in browsers
   - Can be used in Electron apps or server-side code

## Troubleshooting

If you encounter connection issues:

1. **WebSocket Connectivity**:
   - Check if your network allows WebSocket connections
   - Try both secure (wss) and non-secure (ws) WebSocket connections
   - Check browser console for specific error messages

2. **TCP Connectivity**:
   - Verify that port 1883 is not blocked by firewalls
   - Try connecting to different brokers
   - Check for error messages in the Node.js console

3. **Broker Issues**:
   - The public EMQX broker might have connection limits or be temporarily unavailable
   - Consider setting up a local broker for testing

## Setting Up a Local MQTT Broker

For reliable testing, you can set up a local MQTT broker:

### Using Mosquitto

1. Install Mosquitto:
   ```bash
   # macOS
   brew install mosquitto

   # Ubuntu/Debian
   sudo apt-get install mosquitto mosquitto-clients
   ```

2. Start Mosquitto:
   ```bash
   mosquitto -v
   ```

3. Test with the local broker:
   ```bash
   node mqtt-local-test.js
   ```

### Using EMQX Docker Container

1. Pull and run the EMQX Docker image:
   ```bash
   docker run -d --name emqx -p 1883:1883 -p 8083:8083 -p 8084:8084 -p 8883:8883 -p 18083:18083 emqx/emqx
   ```

2. Access the EMQX dashboard at http://localhost:18083 (default credentials: admin/public)

3. Test with the local broker:
   ```bash
   node mqtt-local-test.js
   ```
