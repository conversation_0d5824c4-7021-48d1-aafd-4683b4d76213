{"version": 3, "sources": ["../webpack/bootstrap", "../node_modules/workbox-core/_version.js", "../node_modules/workbox-precaching/_version.js", "../node_modules/workbox-routing/_version.js", "../node_modules/workbox-strategies/_version.js", "../node_modules/workbox-expiration/_version.js", "../node_modules/workbox-core/_private/logger.js", "../node_modules/workbox-core/models/messages/messageGenerator.js", "../node_modules/workbox-core/_private/WorkboxError.js", "../node_modules/workbox-core/_private/assert.js", "../node_modules/workbox-core/models/quotaErrorCallbacks.js", "../node_modules/workbox-core/_private/cacheNames.js", "../node_modules/workbox-core/_private/getFriendlyURL.js", "../node_modules/workbox-core/utils/pluginUtils.js", "../node_modules/workbox-core/_private/cacheWrapper.js", "../node_modules/workbox-core/_private/executeQuotaErrorCallbacks.js", "../node_modules/workbox-core/_private/canConstructReadableStream.js", "../node_modules/workbox-core/_private/canConstructResponseFromBodyStream.js", "../node_modules/workbox-core/_private/dontWaitFor.js", "../node_modules/workbox-core/_private/DBWrapper.js", "../node_modules/workbox-core/_private/deleteDatabase.js", "../node_modules/workbox-core/_private/fetchWrapper.js", "../node_modules/workbox-core/copyResponse.js", "../node_modules/workbox-expiration/models/CacheTimestampsModel.js", "../node_modules/workbox-expiration/CacheExpiration.js", "../node_modules/workbox-precaching/utils/precachePlugins.js", "../node_modules/workbox-precaching/utils/createCacheKey.js", "../node_modules/workbox-precaching/PrecacheController.js", "../node_modules/workbox-precaching/utils/getOrCreatePrecacheController.js", "../node_modules/workbox-precaching/utils/removeIgnoredSearchParams.js", "../node_modules/workbox-precaching/utils/getCacheKeyForURL.js", "../node_modules/workbox-precaching/utils/generateURLVariations.js", "../node_modules/workbox-precaching/addRoute.js", "../node_modules/workbox-precaching/utils/addFetchListener.js", "../node_modules/workbox-precaching/precache.js", "../node_modules/workbox-routing/utils/constants.js", "../node_modules/workbox-routing/utils/normalizeHandler.js", "../node_modules/workbox-routing/Route.js", "../node_modules/workbox-routing/RegExpRoute.js", "../node_modules/workbox-routing/Router.js", "../node_modules/workbox-routing/utils/getOrCreateDefaultRouter.js", "../node_modules/workbox-routing/registerRoute.js", "../node_modules/workbox-strategies/plugins/cacheOkAndOpaquePlugin.js", "../node_modules/workbox-precaching/precacheAndRoute.js", "../node_modules/workbox-core/clientsClaim.js", "service-worker.js", "../node_modules/workbox-precaching/createHandlerBoundToURL.js", "../node_modules/workbox-strategies/StaleWhileRevalidate.js", "../node_modules/workbox-expiration/ExpirationPlugin.js", "../node_modules/workbox-core/registerQuotaErrorCallback.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "self", "_", "e", "messageGenerator", "code", "msg", "_len", "arguments", "length", "args", "Array", "_key", "concat", "JSON", "stringify", "WorkboxError", "Error", "constructor", "errorCode", "details", "super", "this", "quotaErrorCallbacks", "Set", "_cacheNameDetails", "googleAnalytics", "precache", "prefix", "runtime", "suffix", "registration", "scope", "_createCacheName", "cacheName", "filter", "join", "cacheNames", "userCacheName", "getFriendlyURL", "url", "URL", "String", "location", "href", "replace", "RegExp", "origin", "pluginUtils", "plugins", "callback<PERSON><PERSON>", "plugin", "_getEffectiveRequest", "async", "request", "_ref", "cacheKeyWillBeUsedPlugins", "effectiveRequest", "Request", "matchWrapper", "event", "matchOptions", "_ref3", "cache", "caches", "open", "cachedResponse", "match", "pluginMethod", "cacheWrapper", "response", "_ref4", "responseToCache", "_ref2", "pluginsUsed", "status", "undefined", "_isResponseSafeToCache", "updatePlugins", "oldResponse", "put", "error", "callback", "executeQuotaErrorCallbacks", "newResponse", "supportStatus", "dontWait<PERSON>or", "promise", "then", "DBWrapper", "version", "onupgradeneeded", "onversionchange", "_db", "_name", "_version", "_onupgradeneeded", "_onversionchange", "close", "db", "Promise", "resolve", "reject", "openRequestTimedOut", "setTimeout", "OPEN_TIMEOUT", "openRequest", "indexedDB", "onerror", "evt", "transaction", "abort", "result", "onsuccess", "storeName", "query", "getAllKeys", "count", "getAllMatching", "include<PERSON><PERSON>s", "map", "entry", "index", "direction", "txn", "done", "store", "objectStore", "target", "results", "openCursor", "cursor", "push", "continue", "storeNames", "type", "<PERSON>ab<PERSON>", "oncomplete", "method", "objStore", "apply", "methodsToWrap", "readonly", "readwrite", "methods", "entries", "IDBObjectStore", "_len2", "_key2", "_call", "fetchWrapper", "fetchOptions", "FetchEvent", "preloadResponse", "possiblePreloadResponse", "failedFetchPlugins", "originalRequest", "clone", "requestClone", "err", "thrownError", "pluginFilteredRequest", "fetchResponse", "fetch", "process", "copyResponse", "modifier", "clonedResponse", "responseInit", "headers", "Headers", "statusText", "modifiedResponseInit", "body", "testResponse", "Response", "canConstructResponseFromBodyStream", "blob", "OBJECT_STORE_NAME", "normalizeURL", "unNormalizedUrl", "hash", "CacheTimestampsModel", "_cacheName", "_handleUpgrade", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "deleteDatabase", "onblocked", "timestamp", "id", "_getId", "minTimestamp", "maxCount", "entriesToDelete", "entriesNotDeletedCount", "urlsDeleted", "delete", "CacheExpiration", "config", "_isRunning", "_rerunRequested", "_maxEntries", "maxEntries", "_maxAgeSeconds", "maxAgeSeconds", "_timestampModel", "Date", "now", "urlsExpired", "expireEntries", "setTimestamp", "getTimestamp", "Infinity", "precachePlugins", "add", "newPlugins", "createCacheKey", "urlObject", "cache<PERSON>ey", "revision", "cacheKeyURL", "originalURL", "searchParams", "set", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "_urlsToCacheModes", "_cacheKeysToIntegrities", "addToCacheList", "urlsToWarnAbout", "cacheMode", "has", "firstEntry", "secondEntry", "integrity", "warningMessage", "console", "warn", "toBePrecached", "alreadyPrecached", "alreadyCachedRequests", "keys", "existingCacheKeys", "precacheRequests", "_addURLToCache", "all", "updatedURLs", "item", "notUpdatedURLs", "currentlyCachedRequests", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "values", "deletedURLs", "credentials", "cacheWillUpdatePlugin", "cacheWillUpdate", "redirected", "ignoreSearch", "getURLsToCacheKeys", "getCachedURLs", "getCacheKeyForURL", "createHandler", "fallbackToNetwork", "matchPrecache", "createHandlerBoundToURL", "handler", "precacheController", "getOrCreatePrecacheController", "removeIgnoredSearchParams", "ignoreURLParametersMatching", "paramName", "some", "regExp", "test", "options", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "directoryIndex", "cleanURLs", "urlManipulation", "urlWithoutIgnoredParams", "pathname", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "generateURLVariations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listenerAdded", "addRoute", "addEventListener", "precachedURL", "responsePromise", "respondWith", "addFetchListener", "installListener", "waitUntil", "install", "catch", "activateListener", "activate", "normalize<PERSON><PERSON><PERSON>", "handle", "Route", "RegExpRoute", "exec", "slice", "Router", "_routes", "routes", "handleRequest", "addCacheListener", "data", "payload", "requestPromises", "urlsToCache", "ports", "postMessage", "protocol", "startsWith", "params", "route", "findMatchingRoute", "_<PERSON><PERSON><PERSON><PERSON>", "_catch<PERSON><PERSON>ler", "matchResult", "isArray", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCatchHandler", "registerRoute", "unregisterRoute", "routeIndex", "indexOf", "splice", "defaultRouter", "getOrCreateDefaultRouter", "capture", "captureUrl", "moduleName", "funcName", "cacheOkAndOpaquePlugin", "clients", "claim", "__WB_MANIFEST", "fileExtensionRegexp", "_plugins", "isUsingCacheWillUpdate", "_fetchOptions", "_matchOptions", "fetchAndCachePromise", "_getFromNetwork", "cachePutPromise", "cachedResponseWillBeUsed", "isFresh", "_isResponseDateFresh", "cacheExpiration", "_getCacheExpiration", "updateTimestampDone", "updateTimestamp", "cacheDidUpdate", "_config", "_cacheExpirations", "purgeOnQuotaError", "deleteCacheAndMetadata", "dateHeaderTimestamp", "_getDateHeaderTimestamp", "<PERSON><PERSON><PERSON><PERSON>", "headerTime", "getTime", "isNaN", "skipWaiting"], "mappings": "aACE,IAAI,EAAmB,CAAC,EAGxB,SAAS,EAAoB,GAG5B,GAAG,EAAiB,GACnB,OAAO,EAAiB,GAAU,QAGnC,IAAI,EAAS,EAAiB,GAAY,CACzC,EAAG,EACH,GAAG,EACH,QAAS,CAAC,GAUX,OANA,EAAQ,GAAU,KAAK,EAAO,QAAS,EAAQ,EAAO,QAAS,GAG/D,EAAO,GAAI,EAGJ,EAAO,OACf,CAIA,EAAoB,EAAI,EAGxB,EAAoB,EAAI,EAGxB,EAAoB,EAAI,SAAS,EAAS,EAAM,GAC3C,EAAoB,EAAE,EAAS,IAClC,OAAO,eAAe,EAAS,EAAM,CAAE,YAAY,EAAM,IAAK,GAEhE,EAGA,EAAoB,EAAI,SAAS,GACX,qBAAX,QAA0B,OAAO,aAC1C,OAAO,eAAe,EAAS,OAAO,YAAa,CAAE,MAAO,WAE7D,OAAO,eAAe,EAAS,aAAc,CAAE,OAAO,GACvD,EAOA,EAAoB,EAAI,SAAS,EAAO,GAEvC,GADU,EAAP,IAAU,EAAQ,EAAoB,IAC/B,EAAP,EAAU,OAAO,EACpB,GAAW,EAAP,GAA8B,kBAAV,GAAsB,GAAS,EAAM,WAAY,OAAO,EAChF,IAAI,EAAK,OAAO,OAAO,MAGvB,GAFA,EAAoB,EAAE,GACtB,OAAO,eAAe,EAAI,UAAW,CAAE,YAAY,EAAM,MAAO,IACtD,EAAP,GAA4B,iBAAT,EAAmB,IAAI,IAAI,KAAO,EAAO,EAAoB,EAAE,EAAI,EAAK,SAAS,GAAO,OAAO,EAAM,EAAM,EAAE,KAAK,KAAM,IAC9I,OAAO,CACR,EAGA,EAAoB,EAAI,SAAS,GAChC,IAAI,EAAS,GAAU,EAAO,WAC7B,WAAwB,OAAO,EAAgB,OAAG,EAClD,WAA8B,OAAO,CAAQ,EAE9C,OADA,EAAoB,EAAE,EAAQ,IAAK,GAC5B,CACR,EAGA,EAAoB,EAAI,SAAS,EAAQ,GAAY,OAAO,OAAO,UAAU,eAAe,KAAK,EAAQ,EAAW,EAGpH,EAAoB,EAAI,IAIjB,EAAoB,EAAoB,EAAI,kCChFrD,IACI,KAAK,uBAAyB,GAEtB,CAAZ,MAAO,GAAK,gCCHZ,IACI,KAAK,6BAA+B,GAE5B,CAAZ,MAAO,GAAK,gCCHZ,IACI,KAAK,0BAA4B,GAEzB,CAAZ,MAAO,GAAK,gCCHZ,IACI,KAAK,6BAA+B,GAE5B,CAAZ,MAAO,GAAK,gCCHZ,IACI,KAAK,6BAA+B,GAE5B,CAAZ,MAAO,GAAK,4CCEZ,MCgBa,EAdI,SAAC,GACd,IAAI,EAAM,EAAK,QAAA,EAAA,UAAA,OADQ,EAAI,IAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,EAAA,EAAA,IAAJ,EAAI,EAAA,GAAA,UAAA,GAK3B,OAHI,EAAK,OAAS,IACd,GAAO,OAAJ,OAAW,KAAK,UAAU,KAE1B,CACX,ECGA,MAAM,UAAqB,MASvB,YAAY,EAAW,GAEnB,MADgB,EAAiB,EAAW,IAE5C,KAAK,KAAO,EACZ,KAAK,QAAU,CACnB,ECjBJ,MCNM,EAAsB,IAAI,ICDhC,MAAM,EAAoB,CACtB,gBAAiB,kBACjB,SAAU,cACV,OAAQ,UACR,QAAS,UACT,OAAgC,qBAAjB,aAA+B,aAAa,MAAQ,IAEjE,EAAoB,GACf,CAAC,EAAkB,OAAQ,EAAW,EAAkB,QAC1D,QAAQ,GAAU,GAAS,EAAM,OAAS,IAC1C,KAAK,KAOD,EAWS,GACP,GAAiB,EAAiB,EAAkB,UAZtD,EAiBQ,GACN,GAAiB,EAAiB,EAAkB,SCnCnE,MAAM,EAAkB,GACL,IAAI,IAAI,OAAO,GAAM,SAAS,MAG/B,KAAK,QAAQ,IAAI,OAAO,IAAD,OAAK,SAAS,SAAW,ICJrD,EACD,CAAC,EAAS,IACP,EAAQ,QAAQ,GAAW,KAAgB,ICmBpD,EAAuB,UAA4C,IAArC,QAAE,EAAO,KAAE,EAAI,QAAE,EAAU,IAAK,EAChE,MAAM,EAA4B,EAAmB,EAAS,sBAC9D,IAAI,EAAmB,EACvB,IAAK,MAAM,KAAU,EACjB,QAAyB,EAAwD,mBAAE,KAAK,EAAQ,CAAE,OAAM,QAAS,IACjF,kBAArB,IACP,EAAmB,IAAI,QAAQ,IAUvC,OAAO,CAAgB,EA+ErB,EAAe,UAAsE,IAA/D,UAAE,EAAS,QAAE,EAAO,MAAE,EAAK,aAAE,EAAY,QAAE,EAAU,IAAK,EAClF,MAAM,QAAc,KAAK,OAAO,KAAK,GAC/B,QAAyB,EAAqB,CAChD,UAAS,UAAS,KAAM,SAE5B,IAAI,QAAuB,EAAM,MAAM,EAAkB,GASzD,IAAK,MAAM,KAAU,EACjB,GAAI,6BAAiE,EAAQ,CACzE,MAAM,EAAe,EAAoE,yBACzF,QAAuB,EAAa,KAAK,EAAQ,CAC7C,YACA,QACA,eACA,iBACA,QAAS,GAWjB,CAEJ,OAAO,CAAc,EAkFZ,EA/DM,UAAgF,IAAzE,UAAE,EAAS,QAAE,EAAO,SAAE,EAAQ,MAAE,EAAK,QAAE,EAAU,GAAE,aAAE,GAAe,EAS1F,MAAM,QAAyB,EAAqB,CAChD,UAAS,UAAS,KAAM,UAE5B,IAAK,EAKD,MAAM,IAAI,EAAa,6BAA8B,CACjD,IAAK,EAAe,EAAiB,OAG7C,MAAM,OA1IqB,WAAuD,IAAhD,QAAE,EAAO,SAAE,EAAQ,MAAE,EAAK,QAAE,EAAU,IAAK,EACzE,EAAkB,EAClB,GAAc,EAClB,IAAK,MAAM,KAAU,EACjB,GAAI,oBAA6C,EAAQ,CACrD,GAAc,EACd,MAAM,EAAe,EAAgD,gBAerE,GAdA,QAAwB,EAAa,KAAK,EAAQ,CAC9C,UACA,SAAU,EACV,WAWC,EACD,KAER,CAsBJ,OApBK,IAiBD,EAAkB,GAA8C,MAA3B,EAAgB,OACjD,OAAkB,GAEnB,GAAoC,IAAI,EA4FjB,CAAuB,CACjD,QACA,UACA,WACA,QAAS,IAEb,IAAK,EAKD,cAEJ,MAAM,QAAc,KAAK,OAAO,KAAK,GAC/B,EAAgB,EAAmB,EAAS,kBAC5C,EAAc,EAAc,OAAS,QACjC,EAAa,CAAE,YAAW,eAAc,QAAS,IACvD,KAKJ,UACU,EAAM,IAAI,EAAkB,EAQtC,CANA,MAAO,GAKH,KAHmB,uBAAf,EAAM,YCjNlB,iBAKI,IAAK,MAAM,KAAY,QACb,GAQd,CDoMkB,GAEJ,CACV,CACA,IAAK,MAAM,KAAU,QACX,EAA8C,eAAE,KAAK,EAAQ,CAC/D,YACA,QACA,cACA,YAAa,EACb,QAAS,GAEjB,EAES,EAEF,EE3OX,ICAI,ECIG,SAAS,EAAY,GAExB,EAAQ,MAAK,QACjB,CCAO,MAAM,EAUT,YAAY,EAAM,GAAqD,IAA5C,gBAAE,EAAe,gBAAE,GAAkB,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EAChE,KAAK,IAAM,KACX,KAAK,MAAQ,EACb,KAAK,SAAW,EAChB,KAAK,iBAAmB,EACxB,KAAK,iBAAmB,GAAmB,KAAO,KAAK,QAC3D,CAOI,SACA,OAAO,KAAK,GAChB,CAQA,aACI,IAAI,KAAK,IAmCT,OAjCA,KAAK,UAAY,IAAI,SAAQ,CAAC,EAAS,KAMnC,IAAI,GAAsB,EAC1B,YAAW,KACP,GAAsB,EACtB,EAAO,IAAI,MAAM,8CAA8C,GAChE,KAAK,cACR,MAAM,EAAc,UAAU,KAAK,KAAK,MAAO,KAAK,UACpD,EAAY,QAAU,IAAM,EAAO,EAAY,OAC/C,EAAY,gBAAmB,IACvB,GACA,EAAY,YAAY,QACxB,EAAY,OAAO,SAEmB,oBAA1B,KAAK,kBACjB,KAAK,iBAAiB,EAC1B,EAEJ,EAAY,UAAY,KACpB,MAAM,EAAK,EAAY,OACnB,EACA,EAAG,SAGH,EAAG,gBAAkB,KAAK,iBAAiB,KAAK,MAChD,EAAQ,GACZ,CACH,IAEE,IACX,CAUA,aAAa,EAAW,GACpB,aAAc,KAAK,WAAW,EAAW,EAAO,IAAI,EACxD,CAWA,aAAa,EAAW,EAAO,GAC3B,aAAa,KAAK,eAAe,EAAW,CAAE,QAAO,SACzD,CAWA,iBAAiB,EAAW,EAAO,GAE/B,aADsB,KAAK,eAAe,EAAW,CAAE,QAAO,QAAO,aAAa,KACnE,KAAK,GAAU,EAAM,KACxC,CAkBA,qBAAqB,GACmC,IADxB,MAAE,EAAK,MAAE,EAAQ,KAAI,UACrD,EAAY,OAAM,MAAE,EAAK,YAAE,GAAc,GAAQ,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EACjD,aAAa,KAAK,YAAY,CAAC,GAAY,YAAY,CAAC,EAAK,KACzD,MAAM,EAAQ,EAAI,YAAY,GACxB,EAAS,EAAQ,EAAM,MAAM,GAAS,EACtC,EAAU,GACV,EAAU,EAAO,WAAW,EAAO,GACzC,EAAQ,UAAY,KAChB,MAAM,EAAS,EAAQ,OACnB,GACA,EAAQ,KAAK,EAAc,EAAS,EAAO,OACvC,GAAS,EAAQ,QAAU,EAC3B,EAAK,GAGL,EAAO,YAIX,EAAK,EACT,CACH,GAET,CAkBA,kBAAkB,EAAY,EAAM,GAEhC,aADM,KAAK,aACE,IAAI,SAAQ,CAAC,EAAS,KAC/B,MAAM,EAAM,KAAK,IAAI,YAAY,EAAY,GAC7C,EAAI,QAAU,IAAM,EAAO,EAAI,OAC/B,EAAI,WAAa,IAAM,IACvB,EAAS,GAAM,GAAU,EAAQ,IAAO,GAEhD,CAWA,YAAY,EAAQ,EAAW,GAAe,QAAA,EAAA,UAAA,OAAN,EAAI,IAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,EAAA,EAAA,IAAJ,EAAI,EAAA,GAAA,UAAA,GAQxC,aAAa,KAAK,YAAY,CAAC,GAAY,GAP1B,CAAC,EAAK,KACnB,MAAM,EAAW,EAAI,YAAY,GAG3B,EAAU,EAAS,GAAQ,MAAM,EAAU,GACjD,EAAQ,UAAY,IAAM,EAAK,EAAQ,OAAO,GAGtD,CAcA,QACQ,KAAK,MACL,KAAK,IAAI,QACT,KAAK,IAAM,KAEnB,EAIJ,EAAU,UAAU,aAAe,IAEnC,MAAM,EAAgB,CAClB,SAAU,CAAC,MAAO,QAAS,SAAU,SAAU,cAC/C,UAAW,CAAC,MAAO,MAAO,QAAS,WAEvC,IAAK,MAAO,EAAM,KAAY,OAAO,QAAQ,GACzC,IAAK,MAAM,KAAU,EACb,KAAU,eAAe,YAEzB,EAAU,UAAU,GAChB,eAAgB,GAAoB,QAAA,EAAA,UAAA,OAAN,EAAI,IAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,EAAA,EAAA,IAAJ,EAAI,EAAA,GAAA,UAAA,GAC9B,aAAa,KAAK,MAAM,EAAQ,EAAW,KAAS,EACxD,GCtOT,MC4HD,EAjHe,UAA2D,IAApD,QAAE,EAAO,aAAE,EAAY,MAAE,EAAK,QAAE,EAAU,IAAK,EAOvE,GANuB,kBAAZ,IACP,EAAU,IAAI,QAAQ,IAKtB,aAAiB,YAAc,EAAM,gBAAiB,CACtD,MAAM,QAAgC,EAAM,gBAC5C,GAAI,EAKA,OAAO,CAEf,CAUA,MAAM,EAAqB,EAAmB,EAAS,gBAIjD,EAAkB,EAAmB,OAAS,EAChD,EAAQ,QAAU,KACtB,IACI,IAAK,MAAM,KAAU,EACjB,GAAI,qBAA+C,EAAQ,CACvD,MAAM,EAAe,EAAkD,iBACjE,EAAe,EAAQ,QAC7B,QAAgB,EAAa,KAAK,EAAQ,CACtC,QAAS,EACT,SAWR,CAOR,CAJA,MAAO,GACH,MAAM,IAAI,EAAa,kCAAmC,CACtD,YAAa,GAErB,CAIA,MAAM,EAAwB,EAAQ,QACtC,IACI,IAAI,EAGA,EADiB,aAAjB,EAAQ,WACc,MAAM,SAGN,MAAM,EAAS,GAOzC,IAAK,MAAM,KAAU,EACb,oBAA6C,IAC7C,QAAsB,EAAgD,gBACjE,KAAK,EAAQ,CACd,QACA,QAAS,EACT,SAAU,KAatB,OAAO,CAgBX,CAdA,MAAO,GACC,EAIJ,IAAK,MAAM,KAAU,QACX,EAA0C,aAAE,KAAK,EAAQ,CAC3D,QACA,QACA,gBAAiB,EAAgB,QACjC,QAAS,EAAsB,UAGvC,MAAM,CACV,GClHJ,eAAe,EAAa,EAAU,GAClC,MAAM,EAAiB,EAAS,QAE1B,EAAe,CACjB,QAAS,IAAI,QAAQ,EAAe,SACpC,OAAQ,EAAe,OACvB,WAAY,EAAe,YAGzB,EAAuB,EAAW,EAAS,GAAgB,EAI3D,ELpBV,WACI,QAAsB,IAAlB,EAA6B,CAC7B,MAAM,EAAe,IAAI,SAAS,IAClC,GAAI,SAAU,EACV,IACI,IAAI,SAAS,EAAa,MAC1B,GAAgB,CAIpB,CAFA,MAAO,GACH,GAAgB,CACpB,CAEJ,GAAgB,CACpB,CACA,OAAO,CACX,CKKiB,GACT,EAAe,WAAa,EAAe,OAC/C,OAAO,IAAI,SAAS,EAAM,EAC9B,MC/BA,MACM,EAAoB,gBACpB,EAAgB,IAClB,MAAM,EAAM,IAAI,IAAI,EAAiB,SAAS,MAE9C,OADA,EAAI,KAAO,GACJ,EAAI,IAAI,EAOnB,MAAM,EAOF,YAAY,GACR,KAAK,WAAa,EAClB,KAAK,IAAM,IAAI,EArBP,qBAqB0B,EAAG,CACjC,gBAAkB,GAAU,KAAK,eAAe,IAExD,CAQA,eAAe,GACX,MAKM,EALK,EAAM,OAAO,OAKJ,kBAAkB,EAAmB,CAAE,QAAS,OAIpE,EAAS,YAAY,YAAa,YAAa,CAAE,QAAQ,IACzD,EAAS,YAAY,YAAa,YAAa,CAAE,QAAQ,IHpCnC,iBACpB,IAAI,SAAQ,CAAC,EAAS,KACxB,MAAM,EAAU,UAAU,eAAe,GACzC,EAAQ,QAAU,KACd,EAAO,EAAQ,MAAM,EAEzB,EAAQ,UAAY,KAChB,EAAO,IAAI,MAAM,kBAAkB,EAEvC,EAAQ,UAAY,KAChB,GAAS,CACZ,GACH,EG2BE,CAAe,KAAK,WACxB,CAOA,mBAAmB,EAAK,GAEpB,MAAM,EAAQ,CACV,IAFJ,EAAM,EAAa,GAGf,YACA,UAAW,KAAK,WAIhB,GAAI,KAAK,OAAO,UAEd,KAAK,IAAI,IAAI,EAAmB,EAC1C,CASA,mBAAmB,GAEf,aADoB,KAAK,IAAI,IAAI,EAAmB,KAAK,OAAO,KACnD,SACjB,CAYA,oBAAoB,EAAc,GAC9B,MAAM,QAAwB,KAAK,IAAI,YAAY,EAAmB,aAAa,CAAC,EAAK,KACrF,MACM,EADQ,EAAI,YAAY,GACR,MAAM,aAAa,WAAW,KAAM,QACpD,EAAkB,GACxB,IAAI,EAAyB,EAC7B,EAAQ,UAAY,KAChB,MAAM,EAAS,EAAQ,OACvB,GAAI,EAAQ,CACR,MAAM,EAAS,EAAO,MAGlB,EAAO,YAAc,KAAK,aAGrB,GAAgB,EAAO,UAAY,GACnC,GAAY,GAA0B,EASvC,EAAgB,KAAK,EAAO,OAG5B,KAGR,EAAO,UACX,MAEI,EAAK,EACT,CACH,IAMC,EAAc,GACpB,IAAK,MAAM,KAAS,QACV,KAAK,IAAI,OAAO,EAAmB,EAAM,IAC/C,EAAY,KAAK,EAAM,KAE3B,OAAO,CACX,CASA,OAAO,GAIH,OAAO,KAAK,WAAa,IAAM,EAAa,EAChD,EC9IJ,MAAM,EAYF,YAAY,GAAwB,IAAb,EAAM,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EAC7B,KAAK,YAAa,EAClB,KAAK,iBAAkB,EAkCvB,KAAK,YAAc,EAAO,WAC1B,KAAK,eAAiB,EAAO,cAC7B,KAAK,WAAa,EAClB,KAAK,gBAAkB,IAAI,EAAqB,EACpD,CAIA,sBACI,GAAI,KAAK,WAEL,YADA,KAAK,iBAAkB,GAG3B,KAAK,YAAa,EAClB,MAAM,EAAe,KAAK,eACtB,KAAK,MAA+B,IAAtB,KAAK,eAAyB,EAC1C,QAAoB,KAAK,gBAAgB,cAAc,EAAc,KAAK,aAE1E,QAAc,KAAK,OAAO,KAAK,KAAK,YAC1C,IAAK,MAAM,KAAO,QACR,EAAM,OAAO,GAiBvB,KAAK,YAAa,EACd,KAAK,kBACL,KAAK,iBAAkB,EACvB,EAAY,KAAK,iBAEzB,CAQA,sBAAsB,SASZ,KAAK,gBAAgB,aAAa,EAAK,KAAK,MACtD,CAYA,mBAAmB,GACf,GAAK,KAAK,eASL,CAGD,aAFwB,KAAK,gBAAgB,aAAa,GAClC,KAAK,MAA+B,IAAtB,KAAK,cAE/C,CANI,OAAO,CAOf,CAKA,eAGI,KAAK,iBAAkB,QACjB,KAAK,gBAAgB,cAAc,IAC7C,OC7JJ,MAAM,EAAU,GACH,EAAkB,CAK3B,IAAG,IACQ,EAMX,IAAI,GACA,EAAQ,QAAQ,EACpB,GCHG,SAAS,EAAe,GAC3B,IAAK,EACD,MAAM,IAAI,EAAa,oCAAqC,CAAE,UAIlE,GAAqB,kBAAV,EAAoB,CAC3B,MAAM,EAAY,IAAI,IAAI,EAAO,SAAS,MAC1C,MAAO,CACH,SAAU,EAAU,KACpB,IAAK,EAAU,KAEvB,CACA,MAAM,SAAE,EAAQ,IAAE,GAAQ,EAC1B,IAAK,EACD,MAAM,IAAI,EAAa,oCAAqC,CAAE,UAIlE,IAAK,EAAU,CACX,MAAM,EAAY,IAAI,IAAI,EAAK,SAAS,MACxC,MAAO,CACH,SAAU,EAAU,KACpB,IAAK,EAAU,KAEvB,CAGA,MAAM,EAAc,IAAI,IAAI,EAAK,SAAS,MACpC,EAAc,IAAI,IAAI,EAAK,SAAS,MAE1C,OADA,EAAY,aAAa,IAxCC,kBAwC0B,GAC7C,CACH,SAAU,EAAY,KACtB,IAAK,EAAY,KAEzB,CChCA,MAAM,EAOF,YAAY,GACR,KAAK,WAAa,EAA2B,GAC7C,KAAK,iBAAmB,IAAI,IAC5B,KAAK,kBAAoB,IAAI,IAC7B,KAAK,wBAA0B,IAAI,GACvC,CASA,eAAe,GASX,MAAM,EAAkB,GACxB,IAAK,MAAM,KAAS,EAAS,CAEJ,kBAAV,EACP,EAAgB,KAAK,GAEhB,QAA4B,IAAnB,EAAM,UACpB,EAAgB,KAAK,EAAM,KAE/B,MAAM,SAAE,EAAQ,IAAE,GAAQ,EAAe,GACnC,EAA8B,kBAAV,GAAsB,EAAM,SAClD,SAAW,UACf,GAAI,KAAK,iBAAiB,IAAI,IAC1B,KAAK,iBAAiB,IAAI,KAAS,EACnC,MAAM,IAAI,EAAa,wCAAyC,CAC5D,WAAY,KAAK,iBAAiB,IAAI,GACtC,YAAa,IAGrB,GAAqB,kBAAV,GAAsB,EAAM,UAAW,CAC9C,GAAI,KAAK,wBAAwB,IAAI,IACjC,KAAK,wBAAwB,IAAI,KAAc,EAAM,UACrD,MAAM,IAAI,EAAa,4CAA6C,CAChE,QAGR,KAAK,wBAAwB,IAAI,EAAU,EAAM,UACrD,CAGA,GAFA,KAAK,iBAAiB,IAAI,EAAK,GAC/B,KAAK,kBAAkB,IAAI,EAAK,GAC5B,EAAgB,OAAS,EAAG,CAC5B,MAAM,EAAiB,wDAAA,OACV,EAAgB,KAAK,MAAK,kCAAgC,2CAKnE,QAAQ,KAAK,EAKrB,CACJ,CACJ,CAWA,gBAAuC,IAAzB,MAAE,EAAK,QAAE,GAAS,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EAWhC,MAAM,EAAgB,GAChB,EAAmB,GACnB,QAAc,KAAK,OAAO,KAAK,KAAK,YACpC,QAA8B,EAAM,OACpC,EAAoB,IAAI,IAAI,EAAsB,KAAK,GAAY,EAAQ,OACjF,IAAK,MAAO,EAAK,KAAa,KAAK,iBAC3B,EAAkB,IAAI,GACtB,EAAiB,KAAK,GAGtB,EAAc,KAAK,CAAE,WAAU,QAGvC,MAAM,EAAmB,EAAc,KAAI,IAAuB,IAAtB,SAAE,EAAQ,IAAE,GAAK,EACzD,MAAM,EAAY,KAAK,wBAAwB,IAAI,GAC7C,EAAY,KAAK,kBAAkB,IAAI,GAC7C,OAAO,KAAK,eAAe,CACvB,WACA,YACA,QACA,YACA,UACA,OACF,UAEA,QAAQ,IAAI,GAKlB,MAAO,CACH,YALgB,EAAc,KAAK,GAAS,EAAK,MAMjD,eAAgB,EAExB,CAOA,iBACI,MAAM,QAAc,KAAK,OAAO,KAAK,KAAK,YACpC,QAAgC,EAAM,OACtC,EAAoB,IAAI,IAAI,KAAK,iBAAiB,UAClD,EAAc,GACpB,IAAK,MAAM,KAAW,EACb,EAAkB,IAAI,EAAQ,aACzB,EAAM,OAAO,GACnB,EAAY,KAAK,EAAQ,MAMjC,MAAO,CAAE,cACb,CAqBA,qBAAoB,GAA0D,IAAzD,SAAE,EAAQ,IAAE,EAAG,UAAE,EAAS,MAAE,EAAK,QAAE,EAAO,UAAE,GAAW,EACxE,MAAM,EAAU,IAAI,QAAQ,EAAK,CAC7B,YACA,MAAO,EACP,YAAa,gBAEjB,IAQI,EARA,QAAiB,EAAmB,CACpC,QACA,UACA,YAMJ,IAAK,MAAM,KAAW,GAAW,GACzB,oBAAqB,IACrB,EAAwB,GAahC,KAVwB,QAId,EAAsB,gBAAgB,CAAE,QAAO,UAAS,aAG9D,EAAS,OAAS,KAIlB,MAAM,IAAI,EAAa,0BAA2B,CAC9C,MACA,OAAQ,EAAS,SAOrB,EAAS,aACT,QAAiB,EAAa,UAE5B,EAAiB,CACnB,QACA,UACA,WAEA,QAAS,IAAa,EAAM,EAAU,IAAI,QAAQ,GAClD,UAAW,KAAK,WAChB,aAAc,CACV,cAAc,IAG1B,CAOA,qBACI,OAAO,KAAK,gBAChB,CAOA,gBACI,MAAO,IAAI,KAAK,iBAAiB,OACrC,CAUA,kBAAkB,GACd,MAAM,EAAY,IAAI,IAAI,EAAK,SAAS,MACxC,OAAO,KAAK,iBAAiB,IAAI,EAAU,KAC/C,CAkBA,oBAAoB,GAChB,MAAM,EAAM,aAAmB,QAAU,EAAQ,IAAM,EACjD,EAAW,KAAK,kBAAkB,GACxC,GAAI,EAAU,CAEV,aADoB,KAAK,OAAO,KAAK,KAAK,aAC7B,MAAM,EACvB,CAEJ,CAcA,gBAAwC,IAA1B,IAAiB,UAAA,OAAA,QAAA,IAAA,UAAA,KAAA,UAAA,GAC3B,OAAO,UAAuB,IAAhB,QAAE,GAAS,EACrB,IACI,MAAM,QAAiB,KAAK,cAAc,GAC1C,GAAI,EACA,OAAO,EAIX,MAAM,IAAI,EAAa,yBAA0B,CAC7C,UAAW,KAAK,WAChB,IAAK,aAAmB,QAAU,EAAQ,IAAM,GAYxD,CATA,MAAO,GACH,GAAI,EAKA,OAAO,MAAM,GAEjB,MAAM,CACV,EAER,CAeA,wBAAwB,GAA+B,IAA1B,IAAiB,UAAA,OAAA,QAAA,IAAA,UAAA,KAAA,UAAA,GAE1C,IADiB,KAAK,kBAAkB,GAEpC,MAAM,IAAI,EAAa,oBAAqB,CAAE,QAElD,MAAM,EAAU,KAAK,cAAc,GAC7B,EAAU,IAAI,QAAQ,GAC5B,MAAO,IAAM,EAAQ,CAAE,WAC3B,ECxWJ,IAAI,EAKG,MAAM,EAAgC,KACpC,IACD,EAAqB,IAAI,GAEtB,GCEJ,SAAS,EAA0B,GAA6C,IAAlC,EAA2B,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,GAG/E,IAAK,MAAM,IAAa,IAAI,EAAU,aAAa,QAC3C,EAA4B,MAAM,GAAW,EAAO,KAAK,MACzD,EAAU,aAAa,OAAO,GAGtC,OAAO,CACX,CCRO,MAAM,EAAoB,CAAC,EAAK,KACnC,MACM,EADqB,IACgB,qBAC3C,IAAK,MAAM,KCLR,SAAgC,GAAG,IAAE,4BAAE,EAA2B,eAAE,EAAc,UAAE,EAAS,gBAAE,GAAkB,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EAAC,mBACzH,MAAM,EAAY,IAAI,IAAI,EAAK,SAAS,MACxC,EAAU,KAAO,SACX,EAAU,KAChB,MAAM,EAA0B,EAA0B,EAAW,GAErE,SADM,EAAwB,KAC1B,GAAkB,EAAwB,SAAS,SAAS,KAAM,CAClE,MAAM,EAAe,IAAI,IAAI,EAAwB,MACrD,EAAa,UAAY,QACnB,EAAa,IACvB,CACA,GAAI,EAAW,CACX,MAAM,EAAW,IAAI,IAAI,EAAwB,MACjD,EAAS,UAAY,cACf,EAAS,IACnB,CACA,GAAI,EAAiB,CACjB,MAAM,EAAiB,EAAgB,CAAE,IAAK,IAC9C,IAAK,MAAM,KAAgB,QACjB,EAAa,IAE3B,CACJ,CAtB6H,EAsB5H,CDjB6B,CAAsB,EAAK,GAAU,CAC3D,MAAM,EAAmB,EAAgB,IAAI,GAC7C,GAAI,EACA,OAAO,CAEf,GErBJ,IAAI,GAAgB,EAyBpB,SAAS,EAAS,GACT,KCKuB,WAAyH,IAAxH,4BAAE,EAA8B,CAAC,SAAQ,eAAE,EAAiB,aAAY,UAAE,GAAY,EAAI,gBAAE,GAAkB,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EAC/I,MAAM,EAAY,IAElB,KAAK,iBAAiB,SAAW,IAC7B,MAAM,EAAe,EAAkB,EAAM,QAAQ,IAAK,CACtD,YACA,iBACA,8BACA,oBAEJ,IAAK,EAKD,OAEJ,IAAI,EAAkB,KAAK,OAAO,KAAK,GAAW,MAAM,GAC7C,EAAM,MAAM,KACpB,MAAM,GACD,GAUG,MAAM,KAmBjB,EAAM,YAAY,EAAgB,GAE1C,CDvDQ,CAAiB,GACjB,GAAgB,EAExB,CE3BA,MAAM,EAAmB,IACrB,MAAM,EAAqB,IACrB,EAAU,EAAgB,MAChC,EAAM,UAAU,EAAmB,QAAQ,CAAE,QAAO,YAC/C,OAAO,IAMR,MAAM,CAAK,IACZ,EAED,EAAoB,IACtB,MAAM,EAAqB,IAC3B,EAAM,UAAU,EAAmB,WAAW,OCV3C,MCAM,EAAoB,GACzB,GAA8B,kBAAZ,EASX,EAWA,CAAE,OAAQ,GCjBzB,MAAM,EAYF,YAAY,EAAO,GAAiC,IAAxB,EAAM,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GFhBT,ME8BrB,KAAK,QAAU,EAAiB,GAChC,KAAK,MAAQ,EACb,KAAK,OAAS,CAClB,ECzBJ,MAAM,UAAoB,EActB,YAAY,EAAQ,EAAS,GAiCzB,OAxBc,IAAa,IAAZ,IAAE,GAAK,EAClB,MAAM,EAAS,EAAO,KAAK,EAAI,MAE/B,GAAK,IAOA,EAAI,SAAW,SAAS,QAA6B,IAAjB,EAAO,OAYhD,OAAO,EAAO,MAAM,EAAE,GAEb,EAAS,EAC1B,EC1CJ,MAAM,EAIF,cACI,KAAK,QAAU,IAAI,GACvB,CAMI,aACA,OAAO,KAAK,OAChB,CAKA,mBAEI,KAAK,iBAAiB,SAAW,IAC7B,MAAM,QAAE,GAAY,EACd,EAAkB,KAAK,cAAc,CAAE,UAAS,UAClD,GACA,EAAM,YAAY,EACtB,GAER,CAuBA,mBAEI,KAAK,iBAAiB,WAAa,IAC/B,GAAI,EAAM,MAA4B,eAApB,EAAM,KAAK,KAAuB,CAChD,MAAM,QAAE,GAAY,EAAM,KACtB,EAGJ,MAAM,EAAkB,QAAQ,IAAI,EAAQ,YAAY,KAAK,IACpC,kBAAV,IACP,EAAQ,CAAC,IAEb,MAAM,EAAU,IAAI,WAAW,GAC/B,OAAO,KAAK,cAAc,CAAE,WAAU,KAK1C,EAAM,UAAU,GAEZ,EAAM,OAAS,EAAM,MAAM,IAC3B,EAAgB,MAAK,IAAM,EAAM,MAAM,GAAG,aAAY,IAE9D,IAER,CAcA,cAAa,GAAqB,IAApB,QAAE,EAAO,MAAE,GAAO,EAS5B,MAAM,EAAM,IAAI,IAAI,EAAQ,IAAK,SAAS,MAC1C,IAAK,EAAI,SAAS,WAAW,QAIzB,cAEJ,MAAM,OAAE,EAAM,MAAE,GAAU,KAAK,kBAAkB,CAAE,MAAK,UAAS,UACjE,IAAI,EAAU,GAAS,EAAM,QAuB7B,IAPK,GAAW,KAAK,kBAKjB,EAAU,KAAK,kBAEd,EAMD,cAkBJ,IAAI,EACJ,IACI,EAAkB,EAAQ,OAAO,CAAE,MAAK,UAAS,QAAO,UAI5D,CAFA,MAAO,GACH,EAAkB,QAAQ,OAAO,EACrC,CAeA,OAdI,aAA2B,SAAW,KAAK,gBAC3C,EAAkB,EAAgB,OAAO,GAU9B,KAAK,cAAc,OAAO,CAAE,MAAK,UAAS,aAGlD,CACX,CAcA,kBAAiB,GAA0B,IAAzB,IAAE,EAAG,QAAE,EAAO,MAAE,GAAO,EAerC,MAAM,EAAS,KAAK,QAAQ,IAAI,EAAQ,SAAW,GACnD,IAAK,MAAM,KAAS,EAAQ,CACxB,IAAI,EACJ,MAAM,EAAc,EAAM,MAAM,CAAE,MAAK,UAAS,UAChD,GAAI,EAmBA,OAjBA,EAAS,GACL,MAAM,QAAQ,IAAuC,IAAvB,EAAY,QAIpC,EAAY,cAAgB,QACE,IAApC,OAAO,KAAK,GAAa,QAIG,mBAAhB,KAPZ,OAAS,GAcN,CAAE,QAAO,SAExB,CAEA,MAAO,CAAC,CACZ,CAWA,kBAAkB,GACd,KAAK,gBAAkB,EAAiB,EAC5C,CAQA,gBAAgB,GACZ,KAAK,cAAgB,EAAiB,EAC1C,CAMA,cAAc,GAiCL,KAAK,QAAQ,IAAI,EAAM,SACxB,KAAK,QAAQ,IAAI,EAAM,OAAQ,IAInC,KAAK,QAAQ,IAAI,EAAM,QAAQ,KAAK,EACxC,CAMA,gBAAgB,GACZ,IAAK,KAAK,QAAQ,IAAI,EAAM,QACxB,MAAM,IAAI,EAAa,6CAA8C,CACjE,OAAQ,EAAM,SAGtB,MAAM,EAAa,KAAK,QAAQ,IAAI,EAAM,QAAQ,QAAQ,GAC1D,KAAI,GAAc,GAId,MAAM,IAAI,EAAa,yCAHvB,KAAK,QAAQ,IAAI,EAAM,QAAQ,OAAO,EAAY,EAK1D,ECrVJ,IAAI,EAQG,MAAM,EAA2B,KAC/B,IACD,EAAgB,IAAI,EAEpB,EAAc,mBACd,EAAc,oBAEX,GCQX,SAAS,EAAc,EAAS,EAAS,GACrC,IAAI,EACJ,GAAuB,kBAAZ,EAAsB,CAC7B,MAAM,EAAa,IAAI,IAAI,EAAS,SAAS,MACzC,EAgCJ,EAAQ,IAAI,GAZU,IAAa,IAAZ,IAAE,GAAK,EAS1B,OAAO,EAAI,OAAS,EAAW,IAAI,GAGN,EAAS,EAC9C,MACK,GAAI,aAAmB,OAExB,EAAQ,IAAI,EAAY,EAAS,EAAS,QAEzC,GAAuB,oBAAZ,EAEZ,EAAQ,IAAI,EAAM,EAAS,EAAS,OAEnC,MAAI,aAAmB,GAIxB,MAAM,IAAI,EAAa,yBAA0B,CAC7C,WAAY,kBACZ,SAAU,gBACV,UAAW,YANf,EAAQ,CAQZ,CAGA,OAFsB,IACR,cAAc,GACrB,CACX,MCnFO,MAAM,EAAyB,CAWlC,gBAAiB,UAAwB,IAAjB,SAAE,GAAU,EAChC,OAAwB,MAApB,EAAS,QAAsC,IAApB,EAAS,OAC7B,EAEJ,IAAI,GCCnB,IAAmC,ECT/B,KAAK,iBAAiB,YAAY,IAAM,KAAK,QAAQ,UVgCzD,SAAkB,GACa,IACR,eAAe,GAC9B,EAAQ,OAAS,IAKjB,KAAK,iBAAiB,UAAW,GACjC,KAAK,iBAAiB,WAAY,GAE1C,CSjCI,CEJa,moGAAK,eFKlB,EAAS,GEAb,MAAM,EAAsB,IAAI,OAAO,oBCAvC,IAAiC,EDCjC,GAEE,IAAuB,IAAtB,QAAE,EAAO,IAAE,GAAK,EAEf,MAAqB,aAAjB,EAAQ,QAIR,EAAI,SAAS,WAAW,QAIxB,EAAI,SAAS,MAAM,GAIZ,ICjBkB,EDmBkB,cClBpB,IACD,wBAAwB,KDsBtD,GAEE,IAAA,IAAC,IAAE,GAAK,EAAA,OAAK,EAAI,SAAW,KAAK,SAAS,QAAU,EAAI,SAAS,SAAS,OAAO,GACjF,IEhBF,MAaI,cAA0B,IAAd,EAAO,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EAGnB,GAFA,KAAK,WAAa,EAA0B,EAAQ,WACpD,KAAK,SAAW,EAAQ,SAAW,GAC/B,EAAQ,QAAS,CACjB,MAAM,EAAyB,EAAQ,QAAQ,MAAM,KAAa,EAAO,kBACzE,KAAK,SAAW,EACZ,EAAQ,QAAU,CAAC,KAA2B,EAAQ,QAC9D,MAGI,KAAK,SAAW,CAAC,GAErB,KAAK,cAAgB,EAAQ,aAC7B,KAAK,cAAgB,EAAQ,YACjC,CAWA,aAAY,GAAqB,IAApB,MAAE,EAAK,QAAE,GAAS,EAEJ,kBAAZ,IACP,EAAU,IAAI,QAAQ,IAU1B,MAAM,EAAuB,KAAK,gBAAgB,CAAE,UAAS,UAC7D,IAOI,EAPA,QAAiB,EAAmB,CACpC,UAAW,KAAK,WAChB,UACA,QACA,aAAc,KAAK,cACnB,QAAS,KAAK,WAGlB,GAAI,GAKA,GAAI,EACA,IACI,EAAM,UAAU,EAOpB,CALA,MAAO,GACC,CAIR,MAGH,CACG,EAIJ,IACI,QAAiB,CAIrB,CAFA,MAAO,GACH,EAAQ,CACZ,CACJ,CASA,IAAK,EACD,MAAM,IAAI,EAAa,cAAe,CAAE,IAAK,EAAQ,IAAK,UAE9D,OAAO,CACX,CASA,sBAAqB,GAAqB,IAApB,QAAE,EAAO,MAAE,GAAO,EACpC,MAAM,QAAiB,EAAmB,CACtC,UACA,QACA,aAAc,KAAK,cACnB,QAAS,KAAK,WAEZ,EAAkB,EAAiB,CACrC,UAAW,KAAK,WAChB,UACA,SAAU,EAAS,QACnB,QACA,QAAS,KAAK,WAElB,GAAI,EACA,IACI,EAAM,UAAU,EAOpB,CALA,MAAO,GACC,CAIR,CAEJ,OAAO,CACX,GFxHuB,CACvB,UAAW,SACX,QAAS,CAGP,IGxBN,MAUI,cAAyB,IAAb,EAAM,UAAA,OAAA,QAAA,IAAA,UAAA,GAAA,UAAA,GAAG,CAAC,EC1B1B,IAAoC,ED4C5B,KAAK,yBAA2B,UAAyD,IAAlD,MAAE,EAAK,QAAE,EAAO,UAAE,EAAS,eAAE,GAAgB,EAChF,IAAK,EACD,OAAO,KAEX,MAAM,EAAU,KAAK,qBAAqB,GAGpC,EAAkB,KAAK,oBAAoB,GACjD,EAAY,EAAgB,iBAG5B,MAAM,EAAsB,EAAgB,gBAAgB,EAAQ,KACpE,GAAI,EACA,IACI,EAAM,UAAU,EAWpB,CATA,MAAO,GACC,CAQR,CAEJ,OAAO,EAAU,EAAiB,IAAI,EAY1C,KAAK,eAAiB,UAAkC,IAA3B,UAAE,EAAS,QAAE,GAAS,EAe/C,MAAM,EAAkB,KAAK,oBAAoB,SAC3C,EAAgB,gBAAgB,EAAQ,WACxC,EAAgB,eAAe,EA2BzC,KAAK,QAAU,EACf,KAAK,eAAiB,EAAO,cAC7B,KAAK,kBAAoB,IAAI,IACzB,EAAO,oBClIiB,EDmIG,IAAM,KAAK,yBC3H9C,EAAoB,IAAI,GD6HxB,CAUA,oBAAoB,GAChB,GAAI,IAAc,IACd,MAAM,IAAI,EAAa,6BAE3B,IAAI,EAAkB,KAAK,kBAAkB,IAAI,GAKjD,OAJK,IACD,EAAkB,IAAI,EAAgB,EAAW,KAAK,SACtD,KAAK,kBAAkB,IAAI,EAAW,IAEnC,CACX,CAOA,qBAAqB,GACjB,IAAK,KAAK,eAEN,OAAO,EAKX,MAAM,EAAsB,KAAK,wBAAwB,GACzD,GAA4B,OAAxB,EAEA,OAAO,EAKX,OAAO,GADK,KAAK,MAC0C,IAAtB,KAAK,cAC9C,CAUA,wBAAwB,GACpB,IAAK,EAAe,QAAQ,IAAI,QAC5B,OAAO,KAEX,MAAM,EAAa,EAAe,QAAQ,IAAI,QAExC,EADa,IAAI,KAAK,GACE,UAG9B,OAAI,MAAM,GACC,KAEJ,CACX,CAiBA,+BAGI,IAAK,MAAO,EAAW,KAAoB,KAAK,wBACtC,KAAK,OAAO,OAAO,SACnB,EAAgB,SAG1B,KAAK,kBAAoB,IAAI,GACjC,GH1LuB,CAAE,WAAY,SAOzC,KAAK,iBAAiB,WAAY,IAC5B,EAAM,MAA4B,iBAApB,EAAM,KAAK,MAC3B,KAAK,aACP", "file": "service-worker.js", "sourceRoot": "", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 5);\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:5.1.4'] && _();\n}\ncatch (e) { }\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:5.1.4'] && _();\n}\ncatch (e) { }\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:5.1.4'] && _();\n}\ncatch (e) { }\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:5.1.4'] && _();\n}\ncatch (e) { }\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:expiration:5.1.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production' ? null : (() => {\n    // Don't overwrite this value if it's already set.\n    // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-*********\n    if (!('__WB_DISABLE_DEV_LOGS' in self)) {\n        self.__WB_DISABLE_DEV_LOGS = false;\n    }\n    let inGroup = false;\n    const methodToColorMap = {\n        debug: `#7f8c8d`,\n        log: `#2ecc71`,\n        warn: `#f39c12`,\n        error: `#c0392b`,\n        groupCollapsed: `#3498db`,\n        groupEnd: null,\n    };\n    const print = function (method, args) {\n        if (self.__WB_DISABLE_DEV_LOGS) {\n            return;\n        }\n        if (method === 'groupCollapsed') {\n            // Safari doesn't print all console.groupCollapsed() arguments:\n            // https://bugs.webkit.org/show_bug.cgi?id=182754\n            if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                console[method](...args);\n                return;\n            }\n        }\n        const styles = [\n            `background: ${methodToColorMap[method]}`,\n            `border-radius: 0.5em`,\n            `color: white`,\n            `font-weight: bold`,\n            `padding: 2px 0.5em`,\n        ];\n        // When in a group, the workbox prefix is not displayed.\n        const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n        console[method](...logPrefix, ...args);\n        if (method === 'groupCollapsed') {\n            inGroup = true;\n        }\n        if (method === 'groupEnd') {\n            inGroup = false;\n        }\n    };\n    const api = {};\n    const loggerMethods = Object.keys(methodToColorMap);\n    for (const key of loggerMethods) {\n        const method = key;\n        api[method] = (...args) => {\n            print(method, args);\n        };\n    }\n    return api;\n})());\nexport { logger };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messages } from './messages.js';\nimport '../../_version.js';\nconst fallback = (code, ...args) => {\n    let msg = code;\n    if (args.length > 0) {\n        msg += ` :: ${JSON.stringify(args)}`;\n    }\n    return msg;\n};\nconst generatorFunction = (code, details = {}) => {\n    const message = messages[code];\n    if (!message) {\n        throw new Error(`Unable to find message for code '${code}'.`);\n    }\n    return message(details);\n};\nexport const messageGenerator = (process.env.NODE_ENV === 'production') ?\n    fallback : generatorFunction;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messageGenerator } from '../models/messages/messageGenerator.js';\nimport '../_version.js';\n/**\n * Workbox errors should be thrown with this class.\n * This allows use to ensure the type easily in tests,\n * helps developers identify errors from workbox\n * easily and allows use to optimise error\n * messages correctly.\n *\n * @private\n */\nclass WorkboxError extends Error {\n    /**\n     *\n     * @param {string} errorCode The error code that\n     * identifies this particular error.\n     * @param {Object=} details Any relevant arguments\n     * that will help developers identify issues should\n     * be added as a key on the context object.\n     */\n    constructor(errorCode, details) {\n        const message = messageGenerator(errorCode, details);\n        super(message);\n        this.name = errorCode;\n        this.details = details;\n    }\n}\nexport { WorkboxError };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from '../_private/WorkboxError.js';\nimport '../_version.js';\n/*\n * This method throws if the supplied value is not an array.\n * The destructed values are required to produce a meaningful error for users.\n * The destructed and restructured object is so it's clear what is\n * needed.\n */\nconst isArray = (value, details) => {\n    if (!Array.isArray(value)) {\n        throw new WorkboxError('not-an-array', details);\n    }\n};\nconst hasMethod = (object, expectedMethod, details) => {\n    const type = typeof object[expectedMethod];\n    if (type !== 'function') {\n        details['expectedMethod'] = expectedMethod;\n        throw new WorkboxError('missing-a-method', details);\n    }\n};\nconst isType = (object, expectedType, details) => {\n    if (typeof object !== expectedType) {\n        details['expectedType'] = expectedType;\n        throw new WorkboxError('incorrect-type', details);\n    }\n};\nconst isInstance = (object, expectedClass, details) => {\n    if (!(object instanceof expectedClass)) {\n        details['expectedClass'] = expectedClass;\n        throw new WorkboxError('incorrect-class', details);\n    }\n};\nconst isOneOf = (value, validValues, details) => {\n    if (!validValues.includes(value)) {\n        details['validValueDescription'] =\n            `Valid values are ${JSON.stringify(validValues)}.`;\n        throw new WorkboxError('invalid-value', details);\n    }\n};\nconst isArrayOfClass = (value, expectedClass, details) => {\n    const error = new WorkboxError('not-array-of-class', details);\n    if (!Array.isArray(value)) {\n        throw error;\n    }\n    for (const item of value) {\n        if (!(item instanceof expectedClass)) {\n            throw error;\n        }\n    }\n};\nconst finalAssertExports = process.env.NODE_ENV === 'production' ? null : {\n    hasMethod,\n    isArray,\n    isInstance,\n    isOneOf,\n    isType,\n    isArrayOfClass,\n};\nexport { finalAssertExports as assert };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst getFriendlyURL = (url) => {\n    const urlObj = new URL(String(url), location.href);\n    // See https://github.com/GoogleChrome/workbox/issues/2323\n    // We want to include everything, except for the origin if it's same-origin.\n    return urlObj.href.replace(new RegExp(`^${location.origin}`), '');\n};\nexport { getFriendlyURL };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const pluginUtils = {\n    filter: (plugins, callbackName) => {\n        return plugins.filter((plugin) => callbackName in plugin);\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from './assert.js';\nimport { executeQuotaErrorCallbacks } from './executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from './getFriendlyURL.js';\nimport { logger } from './logger.js';\nimport { pluginUtils } from '../utils/pluginUtils.js';\nimport { WorkboxError } from './WorkboxError.js';\nimport '../_version.js';\n/**\n * Checks the list of plugins for the cacheKeyWillBeUsed callback, and\n * executes any of those callbacks found in sequence. The final `Request` object\n * returned by the last plugin is treated as the cache key for cache reads\n * and/or writes.\n *\n * @param {Object} options\n * @param {Request} options.request\n * @param {string} options.mode\n * @param {Array<Object>} [options.plugins=[]]\n * @return {Promise<Request>}\n *\n * @private\n * @memberof module:workbox-core\n */\nconst _getEffectiveRequest = async ({ request, mode, plugins = [], }) => {\n    const cacheKeyWillBeUsedPlugins = pluginUtils.filter(plugins, \"cacheKeyWillBeUsed\" /* CACHE_KEY_WILL_BE_USED */);\n    let effectiveRequest = request;\n    for (const plugin of cacheKeyWillBeUsedPlugins) {\n        effectiveRequest = await plugin[\"cacheKeyWillBeUsed\" /* CACHE_KEY_WILL_BE_USED */].call(plugin, { mode, request: effectiveRequest });\n        if (typeof effectiveRequest === 'string') {\n            effectiveRequest = new Request(effectiveRequest);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(effectiveRequest, Request, {\n                moduleName: 'Plugin',\n                funcName: \"cacheKeyWillBeUsed\" /* CACHE_KEY_WILL_BE_USED */,\n                isReturnValueProblem: true,\n            });\n        }\n    }\n    return effectiveRequest;\n};\n/**\n * This method will call cacheWillUpdate on the available plugins (or use\n * status === 200) to determine if the Response is safe and valid to cache.\n *\n * @param {Object} options\n * @param {Request} options.request\n * @param {Response} options.response\n * @param {Event} [options.event]\n * @param {Array<Object>} [options.plugins=[]]\n * @return {Promise<Response>}\n *\n * @private\n * @memberof module:workbox-core\n */\nconst _isResponseSafeToCache = async ({ request, response, event, plugins = [], }) => {\n    let responseToCache = response;\n    let pluginsUsed = false;\n    for (const plugin of plugins) {\n        if (\"cacheWillUpdate\" /* CACHE_WILL_UPDATE */ in plugin) {\n            pluginsUsed = true;\n            const pluginMethod = plugin[\"cacheWillUpdate\" /* CACHE_WILL_UPDATE */];\n            responseToCache = await pluginMethod.call(plugin, {\n                request,\n                response: responseToCache,\n                event,\n            });\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    assert.isInstance(responseToCache, Response, {\n                        moduleName: 'Plugin',\n                        funcName: \"cacheWillUpdate\" /* CACHE_WILL_UPDATE */,\n                        isReturnValueProblem: true,\n                    });\n                }\n            }\n            if (!responseToCache) {\n                break;\n            }\n        }\n    }\n    if (!pluginsUsed) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (responseToCache) {\n                if (responseToCache.status !== 200) {\n                    if (responseToCache.status === 0) {\n                        logger.warn(`The response for '${request.url}' is an opaque ` +\n                            `response. The caching strategy that you're using will not ` +\n                            `cache opaque responses by default.`);\n                    }\n                    else {\n                        logger.debug(`The response for '${request.url}' returned ` +\n                            `a status code of '${response.status}' and won't be cached as a ` +\n                            `result.`);\n                    }\n                }\n            }\n        }\n        responseToCache = responseToCache && responseToCache.status === 200 ?\n            responseToCache : undefined;\n    }\n    return responseToCache ? responseToCache : null;\n};\n/**\n * This is a wrapper around cache.match().\n *\n * @param {Object} options\n * @param {string} options.cacheName Name of the cache to match against.\n * @param {Request} options.request The Request that will be used to look up\n *     cache entries.\n * @param {Event} [options.event] The event that prompted the action.\n * @param {Object} [options.matchOptions] Options passed to cache.match().\n * @param {Array<Object>} [options.plugins=[]] Array of plugins.\n * @return {Response} A cached response if available.\n *\n * @private\n * @memberof module:workbox-core\n */\nconst matchWrapper = async ({ cacheName, request, event, matchOptions, plugins = [], }) => {\n    const cache = await self.caches.open(cacheName);\n    const effectiveRequest = await _getEffectiveRequest({\n        plugins, request, mode: 'read'\n    });\n    let cachedResponse = await cache.match(effectiveRequest, matchOptions);\n    if (process.env.NODE_ENV !== 'production') {\n        if (cachedResponse) {\n            logger.debug(`Found a cached response in '${cacheName}'.`);\n        }\n        else {\n            logger.debug(`No cached response found in '${cacheName}'.`);\n        }\n    }\n    for (const plugin of plugins) {\n        if (\"cachedResponseWillBeUsed\" /* CACHED_RESPONSE_WILL_BE_USED */ in plugin) {\n            const pluginMethod = plugin[\"cachedResponseWillBeUsed\" /* CACHED_RESPONSE_WILL_BE_USED */];\n            cachedResponse = await pluginMethod.call(plugin, {\n                cacheName,\n                event,\n                matchOptions,\n                cachedResponse,\n                request: effectiveRequest,\n            });\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachedResponse) {\n                    assert.isInstance(cachedResponse, Response, {\n                        moduleName: 'Plugin',\n                        funcName: \"cachedResponseWillBeUsed\" /* CACHED_RESPONSE_WILL_BE_USED */,\n                        isReturnValueProblem: true,\n                    });\n                }\n            }\n        }\n    }\n    return cachedResponse;\n};\n/**\n * Wrapper around cache.put().\n *\n * Will call `cacheDidUpdate` on plugins if the cache was updated, using\n * `matchOptions` when determining what the old entry is.\n *\n * @param {Object} options\n * @param {string} options.cacheName\n * @param {Request} options.request\n * @param {Response} options.response\n * @param {Event} [options.event]\n * @param {Array<Object>} [options.plugins=[]]\n * @param {Object} [options.matchOptions]\n *\n * @private\n * @memberof module:workbox-core\n */\nconst putWrapper = async ({ cacheName, request, response, event, plugins = [], matchOptions, }) => {\n    if (process.env.NODE_ENV !== 'production') {\n        if (request.method && request.method !== 'GET') {\n            throw new WorkboxError('attempt-to-cache-non-get-request', {\n                url: getFriendlyURL(request.url),\n                method: request.method,\n            });\n        }\n    }\n    const effectiveRequest = await _getEffectiveRequest({\n        plugins, request, mode: 'write'\n    });\n    if (!response) {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.error(`Cannot cache non-existent response for ` +\n                `'${getFriendlyURL(effectiveRequest.url)}'.`);\n        }\n        throw new WorkboxError('cache-put-with-no-response', {\n            url: getFriendlyURL(effectiveRequest.url),\n        });\n    }\n    const responseToCache = await _isResponseSafeToCache({\n        event,\n        plugins,\n        response,\n        request: effectiveRequest,\n    });\n    if (!responseToCache) {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' will ` +\n                `not be cached.`, responseToCache);\n        }\n        return;\n    }\n    const cache = await self.caches.open(cacheName);\n    const updatePlugins = pluginUtils.filter(plugins, \"cacheDidUpdate\" /* CACHE_DID_UPDATE */);\n    const oldResponse = updatePlugins.length > 0 ?\n        await matchWrapper({ cacheName, matchOptions, request: effectiveRequest }) :\n        null;\n    if (process.env.NODE_ENV !== 'production') {\n        logger.debug(`Updating the '${cacheName}' cache with a new Response for ` +\n            `${getFriendlyURL(effectiveRequest.url)}.`);\n    }\n    try {\n        await cache.put(effectiveRequest, responseToCache);\n    }\n    catch (error) {\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n        if (error.name === 'QuotaExceededError') {\n            await executeQuotaErrorCallbacks();\n        }\n        throw error;\n    }\n    for (const plugin of updatePlugins) {\n        await plugin[\"cacheDidUpdate\" /* CACHE_DID_UPDATE */].call(plugin, {\n            cacheName,\n            event,\n            oldResponse,\n            newResponse: responseToCache,\n            request: effectiveRequest,\n        });\n    }\n};\nexport const cacheWrapper = {\n    put: putWrapper,\n    match: matchWrapper,\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof module:workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * object.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `ReadableStream`, `false` otherwise.\n *\n * @private\n */\nfunction canConstructReadableStream() {\n    if (supportStatus === undefined) {\n        // See https://github.com/GoogleChrome/workbox/issues/1473\n        try {\n            new ReadableStream({ start() { } });\n            supportStatus = true;\n        }\n        catch (error) {\n            supportStatus = false;\n        }\n    }\n    return supportStatus;\n}\nexport { canConstructReadableStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    promise.then(() => { });\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A class that wraps common IndexedDB functionality in a promise-based API.\n * It exposes all the underlying power and functionality of IndexedDB, but\n * wraps the most commonly used features in a way that's much simpler to use.\n *\n * @private\n */\nexport class DBWrapper {\n    /**\n     * @param {string} name\n     * @param {number} version\n     * @param {Object=} [callback]\n     * @param {!Function} [callbacks.onupgradeneeded]\n     * @param {!Function} [callbacks.onversionchange] Defaults to\n     *     DBWrapper.prototype._onversionchange when not specified.\n     * @private\n     */\n    constructor(name, version, { onupgradeneeded, onversionchange, } = {}) {\n        this._db = null;\n        this._name = name;\n        this._version = version;\n        this._onupgradeneeded = onupgradeneeded;\n        this._onversionchange = onversionchange || (() => this.close());\n    }\n    /**\n     * Returns the IDBDatabase instance (not normally needed).\n     * @return {IDBDatabase|undefined}\n     *\n     * @private\n     */\n    get db() {\n        return this._db;\n    }\n    /**\n     * Opens a connected to an IDBDatabase, invokes any onupgradedneeded\n     * callback, and added an onversionchange callback to the database.\n     *\n     * @return {IDBDatabase}\n     * @private\n     */\n    async open() {\n        if (this._db)\n            return;\n        this._db = await new Promise((resolve, reject) => {\n            // This flag is flipped to true if the timeout callback runs prior\n            // to the request failing or succeeding. Note: we use a timeout instead\n            // of an onblocked handler since there are cases where onblocked will\n            // never never run. A timeout better handles all possible scenarios:\n            // https://github.com/w3c/IndexedDB/issues/223\n            let openRequestTimedOut = false;\n            setTimeout(() => {\n                openRequestTimedOut = true;\n                reject(new Error('The open request was blocked and timed out'));\n            }, this.OPEN_TIMEOUT);\n            const openRequest = indexedDB.open(this._name, this._version);\n            openRequest.onerror = () => reject(openRequest.error);\n            openRequest.onupgradeneeded = (evt) => {\n                if (openRequestTimedOut) {\n                    openRequest.transaction.abort();\n                    openRequest.result.close();\n                }\n                else if (typeof this._onupgradeneeded === 'function') {\n                    this._onupgradeneeded(evt);\n                }\n            };\n            openRequest.onsuccess = () => {\n                const db = openRequest.result;\n                if (openRequestTimedOut) {\n                    db.close();\n                }\n                else {\n                    db.onversionchange = this._onversionchange.bind(this);\n                    resolve(db);\n                }\n            };\n        });\n        return this;\n    }\n    /**\n     * Polyfills the native `getKey()` method. Note, this is overridden at\n     * runtime if the browser supports the native method.\n     *\n     * @param {string} storeName\n     * @param {*} query\n     * @return {Array}\n     * @private\n     */\n    async getKey(storeName, query) {\n        return (await this.getAllKeys(storeName, query, 1))[0];\n    }\n    /**\n     * Polyfills the native `getAll()` method. Note, this is overridden at\n     * runtime if the browser supports the native method.\n     *\n     * @param {string} storeName\n     * @param {*} query\n     * @param {number} count\n     * @return {Array}\n     * @private\n     */\n    async getAll(storeName, query, count) {\n        return await this.getAllMatching(storeName, { query, count });\n    }\n    /**\n     * Polyfills the native `getAllKeys()` method. Note, this is overridden at\n     * runtime if the browser supports the native method.\n     *\n     * @param {string} storeName\n     * @param {*} query\n     * @param {number} count\n     * @return {Array}\n     * @private\n     */\n    async getAllKeys(storeName, query, count) {\n        const entries = await this.getAllMatching(storeName, { query, count, includeKeys: true });\n        return entries.map((entry) => entry.key);\n    }\n    /**\n     * Supports flexible lookup in an object store by specifying an index,\n     * query, direction, and count. This method returns an array of objects\n     * with the signature .\n     *\n     * @param {string} storeName\n     * @param {Object} [opts]\n     * @param {string} [opts.index] The index to use (if specified).\n     * @param {*} [opts.query]\n     * @param {IDBCursorDirection} [opts.direction]\n     * @param {number} [opts.count] The max number of results to return.\n     * @param {boolean} [opts.includeKeys] When true, the structure of the\n     *     returned objects is changed from an array of values to an array of\n     *     objects in the form {key, primaryKey, value}.\n     * @return {Array}\n     * @private\n     */\n    async getAllMatching(storeName, { index, query = null, // IE/Edge errors if query === `undefined`.\n    direction = 'next', count, includeKeys = false, } = {}) {\n        return await this.transaction([storeName], 'readonly', (txn, done) => {\n            const store = txn.objectStore(storeName);\n            const target = index ? store.index(index) : store;\n            const results = [];\n            const request = target.openCursor(query, direction);\n            request.onsuccess = () => {\n                const cursor = request.result;\n                if (cursor) {\n                    results.push(includeKeys ? cursor : cursor.value);\n                    if (count && results.length >= count) {\n                        done(results);\n                    }\n                    else {\n                        cursor.continue();\n                    }\n                }\n                else {\n                    done(results);\n                }\n            };\n        });\n    }\n    /**\n     * Accepts a list of stores, a transaction type, and a callback and\n     * performs a transaction. A promise is returned that resolves to whatever\n     * value the callback chooses. The callback holds all the transaction logic\n     * and is invoked with two arguments:\n     *   1. The IDBTransaction object\n     *   2. A `done` function, that's used to resolve the promise when\n     *      when the transaction is done, if passed a value, the promise is\n     *      resolved to that value.\n     *\n     * @param {Array<string>} storeNames An array of object store names\n     *     involved in the transaction.\n     * @param {string} type Can be `readonly` or `readwrite`.\n     * @param {!Function} callback\n     * @return {*} The result of the transaction ran by the callback.\n     * @private\n     */\n    async transaction(storeNames, type, callback) {\n        await this.open();\n        return await new Promise((resolve, reject) => {\n            const txn = this._db.transaction(storeNames, type);\n            txn.onabort = () => reject(txn.error);\n            txn.oncomplete = () => resolve();\n            callback(txn, (value) => resolve(value));\n        });\n    }\n    /**\n     * Delegates async to a native IDBObjectStore method.\n     *\n     * @param {string} method The method name.\n     * @param {string} storeName The object store name.\n     * @param {string} type Can be `readonly` or `readwrite`.\n     * @param {...*} args The list of args to pass to the native method.\n     * @return {*} The result of the transaction.\n     * @private\n     */\n    async _call(method, storeName, type, ...args) {\n        const callback = (txn, done) => {\n            const objStore = txn.objectStore(storeName);\n            // TODO(philipwalton): Fix this underlying TS2684 error.\n            // @ts-ignore\n            const request = objStore[method].apply(objStore, args);\n            request.onsuccess = () => done(request.result);\n        };\n        return await this.transaction([storeName], type, callback);\n    }\n    /**\n     * Closes the connection opened by `DBWrapper.open()`. Generally this method\n     * doesn't need to be called since:\n     *   1. It's usually better to keep a connection open since opening\n     *      a new connection is somewhat slow.\n     *   2. Connections are automatically closed when the reference is\n     *      garbage collected.\n     * The primary use case for needing to close a connection is when another\n     * reference (typically in another tab) needs to upgrade it and would be\n     * blocked by the current, open connection.\n     *\n     * @private\n     */\n    close() {\n        if (this._db) {\n            this._db.close();\n            this._db = null;\n        }\n    }\n}\n// Exposed on the prototype to let users modify the default timeout on a\n// per-instance or global basis.\nDBWrapper.prototype.OPEN_TIMEOUT = 2000;\n// Wrap native IDBObjectStore methods according to their mode.\nconst methodsToWrap = {\n    readonly: ['get', 'count', 'getKey', 'getAll', 'getAllKeys'],\n    readwrite: ['add', 'put', 'clear', 'delete'],\n};\nfor (const [mode, methods] of Object.entries(methodsToWrap)) {\n    for (const method of methods) {\n        if (method in IDBObjectStore.prototype) {\n            // Don't use arrow functions here since we're outside of the class.\n            DBWrapper.prototype[method] =\n                async function (storeName, ...args) {\n                    return await this._call(method, storeName, mode, ...args);\n                };\n        }\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Deletes the database.\n * Note: this is exported separately from the DBWrapper module because most\n * usages of IndexedDB in workbox dont need deleting, and this way it can be\n * reused in tests to delete databases without creating DBWrapper instances.\n *\n * @param {string} name The database name.\n * @private\n */\nexport const deleteDatabase = async (name) => {\n    await new Promise((resolve, reject) => {\n        const request = indexedDB.deleteDatabase(name);\n        request.onerror = () => {\n            reject(request.error);\n        };\n        request.onblocked = () => {\n            reject(new Error('Delete blocked'));\n        };\n        request.onsuccess = () => {\n            resolve();\n        };\n    });\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from './WorkboxError.js';\nimport { logger } from './logger.js';\nimport { assert } from './assert.js';\nimport { getFriendlyURL } from '../_private/getFriendlyURL.js';\nimport { pluginUtils } from '../utils/pluginUtils.js';\nimport '../_version.js';\n/**\n * Wrapper around the fetch API.\n *\n * Will call requestWillFetch on available plugins.\n *\n * @param {Object} options\n * @param {Request|string} options.request\n * @param {Object} [options.fetchOptions]\n * @param {ExtendableEvent} [options.event]\n * @param {Array<Object>} [options.plugins=[]]\n * @return {Promise<Response>}\n *\n * @private\n * @memberof module:workbox-core\n */\nconst wrappedFetch = async ({ request, fetchOptions, event, plugins = [], }) => {\n    if (typeof request === 'string') {\n        request = new Request(request);\n    }\n    // We *should* be able to call `await event.preloadResponse` even if it's\n    // undefined, but for some reason, doing so leads to errors in our Node unit\n    // tests. To work around that, explicitly check preloadResponse's value first.\n    if (event instanceof FetchEvent && event.preloadResponse) {\n        const possiblePreloadResponse = await event.preloadResponse;\n        if (possiblePreloadResponse) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Using a preloaded navigation response for ` +\n                    `'${getFriendlyURL(request.url)}'`);\n            }\n            return possiblePreloadResponse;\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isInstance(request, Request, {\n            paramName: 'request',\n            expectedClass: Request,\n            moduleName: 'workbox-core',\n            className: 'fetchWrapper',\n            funcName: 'wrappedFetch',\n        });\n    }\n    const failedFetchPlugins = pluginUtils.filter(plugins, \"fetchDidFail\" /* FETCH_DID_FAIL */);\n    // If there is a fetchDidFail plugin, we need to save a clone of the\n    // original request before it's either modified by a requestWillFetch\n    // plugin or before the original request's body is consumed via fetch().\n    const originalRequest = failedFetchPlugins.length > 0 ?\n        request.clone() : null;\n    try {\n        for (const plugin of plugins) {\n            if (\"requestWillFetch\" /* REQUEST_WILL_FETCH */ in plugin) {\n                const pluginMethod = plugin[\"requestWillFetch\" /* REQUEST_WILL_FETCH */];\n                const requestClone = request.clone();\n                request = await pluginMethod.call(plugin, {\n                    request: requestClone,\n                    event,\n                });\n                if (process.env.NODE_ENV !== 'production') {\n                    if (request) {\n                        assert.isInstance(request, Request, {\n                            moduleName: 'Plugin',\n                            funcName: \"cachedResponseWillBeUsed\" /* CACHED_RESPONSE_WILL_BE_USED */,\n                            isReturnValueProblem: true,\n                        });\n                    }\n                }\n            }\n        }\n    }\n    catch (err) {\n        throw new WorkboxError('plugin-error-request-will-fetch', {\n            thrownError: err,\n        });\n    }\n    // The request can be altered by plugins with `requestWillFetch` making\n    // the original request (Most likely from a `fetch` event) to be different\n    // to the Request we make. Pass both to `fetchDidFail` to aid debugging.\n    const pluginFilteredRequest = request.clone();\n    try {\n        let fetchResponse;\n        // See https://github.com/GoogleChrome/workbox/issues/1796\n        if (request.mode === 'navigate') {\n            fetchResponse = await fetch(request);\n        }\n        else {\n            fetchResponse = await fetch(request, fetchOptions);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Network request for ` +\n                `'${getFriendlyURL(request.url)}' returned a response with ` +\n                `status '${fetchResponse.status}'.`);\n        }\n        for (const plugin of plugins) {\n            if (\"fetchDidSucceed\" /* FETCH_DID_SUCCEED */ in plugin) {\n                fetchResponse = await plugin[\"fetchDidSucceed\" /* FETCH_DID_SUCCEED */]\n                    .call(plugin, {\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n                if (process.env.NODE_ENV !== 'production') {\n                    if (fetchResponse) {\n                        assert.isInstance(fetchResponse, Response, {\n                            moduleName: 'Plugin',\n                            funcName: \"fetchDidSucceed\" /* FETCH_DID_SUCCEED */,\n                            isReturnValueProblem: true,\n                        });\n                    }\n                }\n            }\n        }\n        return fetchResponse;\n    }\n    catch (error) {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.error(`Network request for ` +\n                `'${getFriendlyURL(request.url)}' threw an error.`, error);\n        }\n        for (const plugin of failedFetchPlugins) {\n            await plugin[\"fetchDidFail\" /* FETCH_DID_FAIL */].call(plugin, {\n                error,\n                event,\n                originalRequest: originalRequest.clone(),\n                request: pluginFilteredRequest.clone(),\n            });\n        }\n        throw error;\n    }\n};\nconst fetchWrapper = {\n    fetch: wrappedFetch,\n};\nexport { fetchWrapper };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof module:workbox-core\n */\nasync function copyResponse(response, modifier) {\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream() ?\n        clonedResponse.body : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { DBWrapper } from 'workbox-core/_private/DBWrapper.js';\nimport { deleteDatabase } from 'workbox-core/_private/deleteDatabase.js';\nimport '../_version.js';\nconst DB_NAME = 'workbox-expiration';\nconst OBJECT_STORE_NAME = 'cache-entries';\nconst normalizeURL = (unNormalizedUrl) => {\n    const url = new URL(unNormalizedUrl, location.href);\n    url.hash = '';\n    return url.href;\n};\n/**\n * Returns the timestamp model.\n *\n * @private\n */\nclass CacheTimestampsModel {\n    /**\n     *\n     * @param {string} cacheName\n     *\n     * @private\n     */\n    constructor(cacheName) {\n        this._cacheName = cacheName;\n        this._db = new DBWrapper(DB_NAME, 1, {\n            onupgradeneeded: (event) => this._handleUpgrade(event),\n        });\n    }\n    /**\n     * Should perform an upgrade of indexedDB.\n     *\n     * @param {Event} event\n     *\n     * @private\n     */\n    _handleUpgrade(event) {\n        const db = event.target.result;\n        // TODO(philipwalton): EdgeHTML doesn't support arrays as a keyPath, so we\n        // have to use the `id` keyPath here and create our own values (a\n        // concatenation of `url + cacheName`) instead of simply using\n        // `keyPath: ['url', 'cacheName']`, which is supported in other browsers.\n        const objStore = db.createObjectStore(OBJECT_STORE_NAME, { keyPath: 'id' });\n        // TODO(philipwalton): once we don't have to support EdgeHTML, we can\n        // create a single index with the keyPath `['cacheName', 'timestamp']`\n        // instead of doing both these indexes.\n        objStore.createIndex('cacheName', 'cacheName', { unique: false });\n        objStore.createIndex('timestamp', 'timestamp', { unique: false });\n        // Previous versions of `workbox-expiration` used `this._cacheName`\n        // as the IDBDatabase name.\n        deleteDatabase(this._cacheName);\n    }\n    /**\n     * @param {string} url\n     * @param {number} timestamp\n     *\n     * @private\n     */\n    async setTimestamp(url, timestamp) {\n        url = normalizeURL(url);\n        const entry = {\n            url,\n            timestamp,\n            cacheName: this._cacheName,\n            // Creating an ID from the URL and cache name won't be necessary once\n            // Edge switches to Chromium and all browsers we support work with\n            // array keyPaths.\n            id: this._getId(url),\n        };\n        await this._db.put(OBJECT_STORE_NAME, entry);\n    }\n    /**\n     * Returns the timestamp stored for a given URL.\n     *\n     * @param {string} url\n     * @return {number}\n     *\n     * @private\n     */\n    async getTimestamp(url) {\n        const entry = await this._db.get(OBJECT_STORE_NAME, this._getId(url));\n        return entry.timestamp;\n    }\n    /**\n     * Iterates through all the entries in the object store (from newest to\n     * oldest) and removes entries once either `maxCount` is reached or the\n     * entry's timestamp is less than `minTimestamp`.\n     *\n     * @param {number} minTimestamp\n     * @param {number} maxCount\n     * @return {Array<string>}\n     *\n     * @private\n     */\n    async expireEntries(minTimestamp, maxCount) {\n        const entriesToDelete = await this._db.transaction(OBJECT_STORE_NAME, 'readwrite', (txn, done) => {\n            const store = txn.objectStore(OBJECT_STORE_NAME);\n            const request = store.index('timestamp').openCursor(null, 'prev');\n            const entriesToDelete = [];\n            let entriesNotDeletedCount = 0;\n            request.onsuccess = () => {\n                const cursor = request.result;\n                if (cursor) {\n                    const result = cursor.value;\n                    // TODO(philipwalton): once we can use a multi-key index, we\n                    // won't have to check `cacheName` here.\n                    if (result.cacheName === this._cacheName) {\n                        // Delete an entry if it's older than the max age or\n                        // if we already have the max number allowed.\n                        if ((minTimestamp && result.timestamp < minTimestamp) ||\n                            (maxCount && entriesNotDeletedCount >= maxCount)) {\n                            // TODO(philipwalton): we should be able to delete the\n                            // entry right here, but doing so causes an iteration\n                            // bug in Safari stable (fixed in TP). Instead we can\n                            // store the keys of the entries to delete, and then\n                            // delete the separate transactions.\n                            // https://github.com/GoogleChrome/workbox/issues/1978\n                            // cursor.delete();\n                            // We only need to return the URL, not the whole entry.\n                            entriesToDelete.push(cursor.value);\n                        }\n                        else {\n                            entriesNotDeletedCount++;\n                        }\n                    }\n                    cursor.continue();\n                }\n                else {\n                    done(entriesToDelete);\n                }\n            };\n        });\n        // TODO(philipwalton): once the Safari bug in the following issue is fixed,\n        // we should be able to remove this loop and do the entry deletion in the\n        // cursor loop above:\n        // https://github.com/GoogleChrome/workbox/issues/1978\n        const urlsDeleted = [];\n        for (const entry of entriesToDelete) {\n            await this._db.delete(OBJECT_STORE_NAME, entry.id);\n            urlsDeleted.push(entry.url);\n        }\n        return urlsDeleted;\n    }\n    /**\n     * Takes a URL and returns an ID that will be unique in the object store.\n     *\n     * @param {string} url\n     * @return {string}\n     *\n     * @private\n     */\n    _getId(url) {\n        // Creating an ID from the URL and cache name won't be necessary once\n        // Edge switches to Chromium and all browsers we support work with\n        // array keyPaths.\n        return this._cacheName + '|' + normalizeURL(url);\n    }\n}\nexport { CacheTimestampsModel };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheTimestampsModel } from './models/CacheTimestampsModel.js';\nimport './_version.js';\n/**\n * The `CacheExpiration` class allows you define an expiration and / or\n * limit on the number of responses stored in a\n * [`Cache`](https://developer.mozilla.org/en-US/docs/Web/API/Cache).\n *\n * @memberof module:workbox-expiration\n */\nclass CacheExpiration {\n    /**\n     * To construct a new CacheExpiration instance you must provide at least\n     * one of the `config` properties.\n     *\n     * @param {string} cacheName Name of the cache to apply restrictions to.\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     */\n    constructor(cacheName, config = {}) {\n        this._isRunning = false;\n        this._rerunRequested = false;\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(cacheName, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'constructor',\n                paramName: 'cacheName',\n            });\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n                // TODO: Assert is positive\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n                // TODO: Assert is positive\n            }\n        }\n        this._maxEntries = config.maxEntries;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheName = cacheName;\n        this._timestampModel = new CacheTimestampsModel(cacheName);\n    }\n    /**\n     * Expires entries for the given cache and given criteria.\n     */\n    async expireEntries() {\n        if (this._isRunning) {\n            this._rerunRequested = true;\n            return;\n        }\n        this._isRunning = true;\n        const minTimestamp = this._maxAgeSeconds ?\n            Date.now() - (this._maxAgeSeconds * 1000) : 0;\n        const urlsExpired = await this._timestampModel.expireEntries(minTimestamp, this._maxEntries);\n        // Delete URLs from the cache\n        const cache = await self.caches.open(this._cacheName);\n        for (const url of urlsExpired) {\n            await cache.delete(url);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (urlsExpired.length > 0) {\n                logger.groupCollapsed(`Expired ${urlsExpired.length} ` +\n                    `${urlsExpired.length === 1 ? 'entry' : 'entries'} and removed ` +\n                    `${urlsExpired.length === 1 ? 'it' : 'them'} from the ` +\n                    `'${this._cacheName}' cache.`);\n                logger.log(`Expired the following ${urlsExpired.length === 1 ?\n                    'URL' : 'URLs'}:`);\n                urlsExpired.forEach((url) => logger.log(`    ${url}`));\n                logger.groupEnd();\n            }\n            else {\n                logger.debug(`Cache expiration ran and found no entries to remove.`);\n            }\n        }\n        this._isRunning = false;\n        if (this._rerunRequested) {\n            this._rerunRequested = false;\n            dontWaitFor(this.expireEntries());\n        }\n    }\n    /**\n     * Update the timestamp for the given URL. This ensures the when\n     * removing entries based on maximum entries, most recently used\n     * is accurate or when expiring, the timestamp is up-to-date.\n     *\n     * @param {string} url\n     */\n    async updateTimestamp(url) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(url, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'updateTimestamp',\n                paramName: 'url',\n            });\n        }\n        await this._timestampModel.setTimestamp(url, Date.now());\n    }\n    /**\n     * Can be used to check if a URL has expired or not before it's used.\n     *\n     * This requires a look up from IndexedDB, so can be slow.\n     *\n     * Note: This method will not remove the cached entry, call\n     * `expireEntries()` to remove indexedDB and Cache entries.\n     *\n     * @param {string} url\n     * @return {boolean}\n     */\n    async isURLExpired(url) {\n        if (!this._maxAgeSeconds) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new WorkboxError(`expired-test-without-max-age`, {\n                    methodName: 'isURLExpired',\n                    paramName: 'maxAgeSeconds',\n                });\n            }\n            return false;\n        }\n        else {\n            const timestamp = await this._timestampModel.getTimestamp(url);\n            const expireOlderThan = Date.now() - (this._maxAgeSeconds * 1000);\n            return (timestamp < expireOlderThan);\n        }\n    }\n    /**\n     * Removes the IndexedDB object store used to keep track of cache expiration\n     * metadata.\n     */\n    async delete() {\n        // Make sure we don't attempt another rerun if we're called in the middle of\n        // a cache expiration.\n        this._rerunRequested = false;\n        await this._timestampModel.expireEntries(Infinity); // Expires all.\n    }\n}\nexport { CacheExpiration };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst plugins = [];\nexport const precachePlugins = {\n    /*\n     * @return {Array}\n     * @private\n     */\n    get() {\n        return plugins;\n    },\n    /*\n     * @param {Array} newPlugins\n     * @private\n     */\n    add(newPlugins) {\n        plugins.push(...newPlugins);\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof module:workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { cacheWrapper } from 'workbox-core/_private/cacheWrapper.js';\nimport { fetchWrapper } from 'workbox-core/_private/fetchWrapper.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof module:workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {string} [cacheName] An optional name for the cache, to override\n     * the default precache name.\n     */\n    constructor(cacheName) {\n        this._cacheName = cacheNames.getPrecacheName(cacheName);\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {\n     * Array<module:workbox-precaching.PrecacheController.PrecacheEntry|string>\n     * } entries Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = (typeof entry !== 'string' && entry.revision) ?\n                'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * @param {Object} options\n     * @param {Event} [options.event] The install event (if needed).\n     * @param {Array<Object>} [options.plugins] Plugins to be used for fetching\n     * and caching during install.\n     * @return {Promise<module:workbox-precaching.InstallResult>}\n     */\n    async install({ event, plugins } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (plugins) {\n                assert.isArray(plugins, {\n                    moduleName: 'workbox-precaching',\n                    className: 'PrecacheController',\n                    funcName: 'install',\n                    paramName: 'plugins',\n                });\n            }\n        }\n        const toBePrecached = [];\n        const alreadyPrecached = [];\n        const cache = await self.caches.open(this._cacheName);\n        const alreadyCachedRequests = await cache.keys();\n        const existingCacheKeys = new Set(alreadyCachedRequests.map((request) => request.url));\n        for (const [url, cacheKey] of this._urlsToCacheKeys) {\n            if (existingCacheKeys.has(cacheKey)) {\n                alreadyPrecached.push(url);\n            }\n            else {\n                toBePrecached.push({ cacheKey, url });\n            }\n        }\n        const precacheRequests = toBePrecached.map(({ cacheKey, url }) => {\n            const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n            const cacheMode = this._urlsToCacheModes.get(url);\n            return this._addURLToCache({\n                cacheKey,\n                cacheMode,\n                event,\n                integrity,\n                plugins,\n                url,\n            });\n        });\n        await Promise.all(precacheRequests);\n        const updatedURLs = toBePrecached.map((item) => item.url);\n        if (process.env.NODE_ENV !== 'production') {\n            printInstallDetails(updatedURLs, alreadyPrecached);\n        }\n        return {\n            updatedURLs,\n            notUpdatedURLs: alreadyPrecached,\n        };\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * @return {Promise<module:workbox-precaching.CleanupResult>}\n     */\n    async activate() {\n        const cache = await self.caches.open(this._cacheName);\n        const currentlyCachedRequests = await cache.keys();\n        const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n        const deletedURLs = [];\n        for (const request of currentlyCachedRequests) {\n            if (!expectedCacheKeys.has(request.url)) {\n                await cache.delete(request);\n                deletedURLs.push(request.url);\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            printCleanupDetails(deletedURLs);\n        }\n        return { deletedURLs };\n    }\n    /**\n     * Requests the entry and saves it to the cache if the response is valid.\n     * By default, any response with a status code of less than 400 (including\n     * opaque responses) is considered valid.\n     *\n     * If you need to use custom criteria to determine what's valid and what\n     * isn't, then pass in an item in `options.plugins` that implements the\n     * `cacheWillUpdate()` lifecycle event.\n     *\n     * @private\n     * @param {Object} options\n     * @param {string} options.cacheKey The string to use a cache key.\n     * @param {string} options.url The URL to fetch and cache.\n     * @param {string} [options.cacheMode] The cache mode for the network request.\n     * @param {Event} [options.event] The install event (if passed).\n     * @param {Array<Object>} [options.plugins] An array of plugins to apply to\n     * fetch and caching.\n     * @param {string} [options.integrity] The value to use for the `integrity`\n     * field when making the request.\n     */\n    async _addURLToCache({ cacheKey, url, cacheMode, event, plugins, integrity }) {\n        const request = new Request(url, {\n            integrity,\n            cache: cacheMode,\n            credentials: 'same-origin',\n        });\n        let response = await fetchWrapper.fetch({\n            event,\n            plugins,\n            request,\n        });\n        // Allow developers to override the default logic about what is and isn't\n        // valid by passing in a plugin implementing cacheWillUpdate(), e.g.\n        // a `CacheableResponsePlugin` instance.\n        let cacheWillUpdatePlugin;\n        for (const plugin of (plugins || [])) {\n            if ('cacheWillUpdate' in plugin) {\n                cacheWillUpdatePlugin = plugin;\n            }\n        }\n        const isValidResponse = cacheWillUpdatePlugin ?\n            // Use a callback if provided. It returns a truthy value if valid.\n            // NOTE: invoke the method on the plugin instance so the `this` context\n            // is correct.\n            await cacheWillUpdatePlugin.cacheWillUpdate({ event, request, response }) :\n            // Otherwise, default to considering any response status under 400 valid.\n            // This includes, by default, considering opaque responses valid.\n            response.status < 400;\n        // Consider this a failure, leading to the `install` handler failing, if\n        // we get back an invalid response.\n        if (!isValidResponse) {\n            throw new WorkboxError('bad-precaching-response', {\n                url,\n                status: response.status,\n            });\n        }\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        if (response.redirected) {\n            response = await copyResponse(response);\n        }\n        await cacheWrapper.put({\n            event,\n            plugins,\n            response,\n            // `request` already uses `url`. We may be able to reuse it.\n            request: cacheKey === url ? request : new Request(cacheKey),\n            cacheName: this._cacheName,\n            matchOptions: {\n                ignoreSearch: true,\n            },\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * This acts as a drop-in replacement for [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this._cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that can be used within a\n     * {@link module:workbox-routing.Route} that will find a response for the\n     * incoming request against the precache.\n     *\n     * If for an unexpected reason there is a cache miss for the request,\n     * this will fall back to retrieving the `Response` via `fetch()` when\n     * `fallbackToNetwork` is `true`.\n     *\n     * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n     * response from the network if there's a precache miss.\n     * @return {module:workbox-routing~handlerCallback}\n     */\n    createHandler(fallbackToNetwork = true) {\n        return async ({ request }) => {\n            try {\n                const response = await this.matchPrecache(request);\n                if (response) {\n                    return response;\n                }\n                // This shouldn't normally happen, but there are edge cases:\n                // https://github.com/GoogleChrome/workbox/issues/1441\n                throw new WorkboxError('missing-precache-entry', {\n                    cacheName: this._cacheName,\n                    url: request instanceof Request ? request.url : request,\n                });\n            }\n            catch (error) {\n                if (fallbackToNetwork) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logger.debug(`Unable to respond with precached response. ` +\n                            `Falling back to network.`, error);\n                    }\n                    return fetch(request);\n                }\n                throw error;\n            }\n        };\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * If for an unexpected reason there is a cache miss when looking up `url`,\n     * this will fall back to retrieving the `Response` via `fetch()` when\n     * `fallbackToNetwork` is `true`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n     * response from the network if there's a precache miss.\n     * @return {module:workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url, fallbackToNetwork = true) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        const handler = this.createHandler(fallbackToNetwork);\n        const request = new Request(url);\n        return () => handler({ request });\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof module:workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './getOrCreatePrecacheController.js';\nimport { generateURLVariations } from './generateURLVariations.js';\nimport '../_version.js';\n/**\n * This function will take the request URL and manipulate it based on the\n * configuration options.\n *\n * @param {string} url\n * @param {Object} options\n * @return {string} Returns the URL in the cache that matches the request,\n * if possible.\n *\n * @private\n */\nexport const getCacheKeyForURL = (url, options) => {\n    const precacheController = getOrCreatePrecacheController();\n    const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n    for (const possibleURL of generateURLVariations(url, options)) {\n        const possibleCacheKey = urlsToCacheKeys.get(possibleURL);\n        if (possibleCacheKey) {\n            return possibleCacheKey;\n        }\n    }\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof module:workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching, directoryIndex, cleanURLs, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addFetchListener } from './utils/addFetchListener.js';\nimport './_version.js';\nlet listenerAdded = false;\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options]\n * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n * check cache entries for a URLs ending with '/' to see if there is a hit when\n * appending the `directoryIndex` value.\n * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/]] An\n * array of regex's to remove search params when looking for a cache match.\n * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n * check the cache for the URL with a `.html` added to the end of the end.\n * @param {module:workbox-precaching~urlManipulation} [options.urlManipulation]\n * This is a function that should take a URL and return an array of\n * alternative URLs that should be checked for precache matches.\n *\n * @memberof module:workbox-precaching\n */\nfunction addRoute(options) {\n    if (!listenerAdded) {\n        addFetchListener(options);\n        listenerAdded = true;\n    }\n}\nexport { addRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getCacheKeyForURL } from './getCacheKeyForURL.js';\nimport '../_version.js';\n/**\n * Adds a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * NOTE: when called more than once this method will replace the previously set\n * configuration options. Calling it more than once is not recommended outside\n * of tests.\n *\n * @private\n * @param {Object} [options]\n * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n * check cache entries for a URLs ending with '/' to see if there is a hit when\n * appending the `directoryIndex` value.\n * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/]] An\n * array of regex's to remove search params when looking for a cache match.\n * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n * check the cache for the URL with a `.html` added to the end of the end.\n * @param {workbox.precaching~urlManipulation} [options.urlManipulation]\n * This is a function that should take a URL and return an array of\n * alternative URLs that should be checked for precache matches.\n */\nexport const addFetchListener = ({ ignoreURLParametersMatching = [/^utm_/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) => {\n    const cacheName = cacheNames.getPrecacheName();\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n    self.addEventListener('fetch', ((event) => {\n        const precachedURL = getCacheKeyForURL(event.request.url, {\n            cleanURLs,\n            directoryIndex,\n            ignoreURLParametersMatching,\n            urlManipulation,\n        });\n        if (!precachedURL) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` +\n                    getFriendlyURL(event.request.url));\n            }\n            return;\n        }\n        let responsePromise = self.caches.open(cacheName).then((cache) => {\n            return cache.match(precachedURL);\n        }).then((cachedResponse) => {\n            if (cachedResponse) {\n                return cachedResponse;\n            }\n            // Fall back to the network if we don't have a cached response\n            // (perhaps due to manual cache cleanup).\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(precachedURL)} in ${cacheName} was not found. ` +\n                    `Falling back to the network instead.`);\n            }\n            return fetch(precachedURL);\n        });\n        if (process.env.NODE_ENV !== 'production') {\n            responsePromise = responsePromise.then((response) => {\n                // Workbox is going to handle the route.\n                // print the routing details to the console.\n                logger.groupCollapsed(`Precaching is responding to: ` +\n                    getFriendlyURL(event.request.url));\n                logger.log(`Serving the precached url: ${precachedURL}`);\n                logger.groupCollapsed(`View request details here.`);\n                logger.log(event.request);\n                logger.groupEnd();\n                logger.groupCollapsed(`View response details here.`);\n                logger.log(response);\n                logger.groupEnd();\n                logger.groupEnd();\n                return response;\n            });\n        }\n        event.respondWith(responsePromise);\n    }));\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { precachePlugins } from './utils/precachePlugins.js';\nimport './_version.js';\nconst installListener = (event) => {\n    const precacheController = getOrCreatePrecacheController();\n    const plugins = precachePlugins.get();\n    event.waitUntil(precacheController.install({ event, plugins })\n        .catch((error) => {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.error(`Service worker installation failed. It will ` +\n                `be retried automatically during the next navigation.`);\n        }\n        // Re-throw the error to ensure installation fails.\n        throw error;\n    }));\n};\nconst activateListener = (event) => {\n    const precacheController = getOrCreatePrecacheController();\n    event.waitUntil(precacheController.activate());\n};\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * [\"precache cache\"]{@link module:workbox-core.cacheNames} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * [addRoute()]{@link module:workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * [precacheAndRoute()]{@link module:workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof module:workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.addToCacheList(entries);\n    if (entries.length > 0) {\n        // NOTE: these listeners will only be added once (even if the `precache()`\n        // method is called multiple times) because event listeners are implemented\n        // as a set, where each listener must be unique.\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('install', installListener);\n        self.addEventListener('activate', activateListener);\n    }\n}\nexport { precache };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof module:workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {module:workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * [Route]{@link module:workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * [See the module docs for info.]{@link https://developers.google.com/web/tools/workbox/modules/workbox-routing}\n *\n * @memberof module:workbox-routing\n * @extends module:workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * [handler's]{@link module:workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if ((url.origin !== location.origin) && (result.index !== 0)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp}' only partially matched ` +\n                        `against the cross-origin URL '${url}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * The Router can be used to process a FetchEvent through one or more\n * [Routes]{@link module:workbox-routing.Route} responding  with a Request if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof module:workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n    }\n    /**\n     * @return {Map<string, Array<module:workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            if (event.data && event.data.type === 'CACHE_URLS') {\n                const { payload } = event.data;\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle (this is usually\n     *     from a fetch event, but it does not have to be).\n     * @param {FetchEvent} [options.event] The event that triggered the request,\n     *     if applicable.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const { params, route } = this.findMatchingRoute({ url, request, event });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([\n                    `Found a route to handle this request:`, route,\n                ]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`, params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        if (!handler && this._defaultHandler) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler.`);\n            }\n            handler = this._defaultHandler;\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        if (responsePromise instanceof Promise && this._catchHandler) {\n            responsePromise = responsePromise.catch((err) => {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Still include URL here as it will be async from the console group\n                    // and may not make sense without the URL\n                    logger.groupCollapsed(`Error thrown when responding to: ` +\n                        ` ${getFriendlyURL(url)}. Falling back to Catch Handler.`);\n                    logger.error(`Error thrown by:`, route);\n                    logger.error(err);\n                    logger.groupEnd();\n                }\n                return this._catchHandler.handle({ url, request, event });\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {Request} options.request The request to match.\n     * @param {Event} [options.event] The corresponding event (unless N/A).\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, request, event }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(url, URL, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'findMatchingRoute',\n                paramName: 'options.url',\n            });\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'findMatchingRoute',\n                paramName: 'options.request',\n            });\n        }\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            const matchResult = route.match({ url, request, event });\n            if (matchResult) {\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                params = matchResult;\n                if (Array.isArray(matchResult) && matchResult.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if ((matchResult.constructor === Object &&\n                    Object.keys(matchResult).length === 0)) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setDefaultHandler(handler) {\n        this._defaultHandler = normalizeHandler(handler);\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {module:workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {module:workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {module:workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call [registerRoute()]{@link module:workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|module:workbox-routing.Route~matchCallback|module:workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {module:workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {module:workbox-routing.Route} The generated `Route`(Useful for\n * unregistering).\n *\n * @memberof module:workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http') ?\n                captureUrl.pathname : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if ((new RegExp(`${wildcards}`)).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if ((url.pathname === captureUrl.pathname) &&\n                    (url.origin !== captureUrl.origin)) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const cacheOkAndOpaquePlugin = {\n    /**\n     * Returns a valid response (to allow caching) if the status is 200 (OK) or\n     * 0 (opaque).\n     *\n     * @param {Object} options\n     * @param {Response} options.response\n     * @return {Response|null}\n     *\n     * @private\n     */\n    cacheWillUpdate: async ({ response }) => {\n        if (response.status === 200 || response.status === 0) {\n            return response;\n        }\n        return null;\n    },\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * [precache()]{@link module:workbox-precaching.precache} and\n * [addRoute()]{@link module:workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See\n * [addRoute() options]{@link module:workbox-precaching.addRoute}.\n *\n * @memberof module:workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Claim any currently available clients once the service worker\n * becomes active. This is normally used in conjunction with `skipWaiting()`.\n *\n * @memberof module:workbox-core\n */\nfunction clientsClaim() {\n    self.addEventListener('activate', () => self.clients.claim());\n}\nexport { clientsClaim };\n", "/* eslint-disable no-restricted-globals */\n\n// This service worker can be customized!\n// See https://developers.google.com/web/tools/workbox/modules\n// for the list of available Workbox modules, or add any other\n// code you'd like.\n// You can also remove this file if you'd prefer not to use a\n// service worker, and the Workbox build step will be skipped.\n\nimport { clientsClaim } from 'workbox-core';\nimport { ExpirationPlugin } from 'workbox-expiration';\nimport { precacheAndRoute, createHandlerBoundToURL } from 'workbox-precaching';\nimport { registerRoute } from 'workbox-routing';\nimport { StaleWhileRevalidate } from 'workbox-strategies';\n\nclientsClaim();\n\n// Precache all of the assets generated by your build process.\n// Their URLs are injected into the manifest variable below.\n// This variable must be present somewhere in your service worker file,\n// even if you decide not to use precaching. See https://cra.link/PWA\nprecacheAndRoute(self.__WB_MANIFEST);\n\n// Set up App Shell-style routing, so that all navigation requests\n// are fulfilled with your index.html shell. Learn more at\n// https://developers.google.com/web/fundamentals/architecture/app-shell\nconst fileExtensionRegexp = new RegExp('/[^/?]+\\\\.[^/]+$');\nregisterRoute(\n  // Return false to exempt requests from being fulfilled by index.html.\n  ({ request, url }) => {\n    // If this isn't a navigation, skip.\n    if (request.mode !== 'navigate') {\n      return false;\n    } // If this is a URL that starts with /_, skip.\n\n    if (url.pathname.startsWith('/_')) {\n      return false;\n    } // If this looks like a URL for a resource, because it contains // a file extension, skip.\n\n    if (url.pathname.match(fileExtensionRegexp)) {\n      return false;\n    } // Return true to signal that we want to use the handler.\n\n    return true;\n  },\n  createHandlerBoundToURL(process.env.PUBLIC_URL + '/index.html')\n);\n\n// An example runtime caching route for requests that aren't handled by the\n// precache, in this case same-origin .png requests like those from in public/\nregisterRoute(\n  // Add in any other file extensions or routing criteria as needed.\n  ({ url }) => url.origin === self.location.origin && url.pathname.endsWith('.png'), // Customize this strategy as needed, e.g., by changing to CacheFirst.\n  new StaleWhileRevalidate({\n    cacheName: 'images',\n    plugins: [\n      // Ensure that once this runtime cache reaches a maximum size the\n      // least-recently used images are removed.\n      new ExpirationPlugin({ maxEntries: 50 }),\n    ],\n  })\n);\n\n// This allows the web app to trigger skipWaiting via\n// registration.waiting.postMessage({type: 'SKIP_WAITING'})\nself.addEventListener('message', (event) => {\n  if (event.data && event.data.type === 'SKIP_WAITING') {\n    self.skipWaiting();\n  }\n});\n\n// Any other custom service worker logic can go here.\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {module:workbox-routing~handlerCallback}\n *\n * @memberof module:workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { cacheWrapper } from 'workbox-core/_private/cacheWrapper.js';\nimport { fetchWrapper } from 'workbox-core/_private/fetchWrapper.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { messages } from './utils/messages.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [stale-while-revalidate]{@link https://developers.google.com/web/fundamentals/instant-and-offline/offline-cookbook/#stale-while-revalidate}\n * request strategy.\n *\n * Resources are requested from both the cache and the network in parallel.\n * The strategy will respond with the cached version if available, otherwise\n * wait for the network response. The cache is updated with the network response\n * with each successful request.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses]{@link https://developers.google.com/web/tools/workbox/guides/handle-third-party-requests}.\n * Opaque responses are cross-origin requests where the response doesn't\n * support [CORS]{@link https://enable-cors.org/}.\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @memberof module:workbox-strategies\n */\nclass StaleWhileRevalidate {\n    /**\n     * @param {Object} options\n     * @param {string} options.cacheName Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * [workbox-core]{@link module:workbox-core.cacheNames}.\n     * @param {Array<Object>} options.plugins [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} options.fetchOptions Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of all fetch() requests made by this strategy.\n     * @param {Object} options.matchOptions [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     */\n    constructor(options = {}) {\n        this._cacheName = cacheNames.getRuntimeName(options.cacheName);\n        this._plugins = options.plugins || [];\n        if (options.plugins) {\n            const isUsingCacheWillUpdate = options.plugins.some((plugin) => !!plugin.cacheWillUpdate);\n            this._plugins = isUsingCacheWillUpdate ?\n                options.plugins : [cacheOkAndOpaquePlugin, ...options.plugins];\n        }\n        else {\n            // No plugins passed in, use the default plugin.\n            this._plugins = [cacheOkAndOpaquePlugin];\n        }\n        this._fetchOptions = options.fetchOptions;\n        this._matchOptions = options.matchOptions;\n    }\n    /**\n     * This method will perform a request strategy and follows an API that\n     * will work with the\n     * [Workbox Router]{@link module:workbox-routing.Router}.\n     *\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {Event} [options.event] The event that triggered the request.\n     * @return {Promise<Response>}\n     */\n    async handle({ event, request }) {\n        const logs = [];\n        if (typeof request === 'string') {\n            request = new Request(request);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: 'StaleWhileRevalidate',\n                funcName: 'handle',\n                paramName: 'request',\n            });\n        }\n        const fetchAndCachePromise = this._getFromNetwork({ request, event });\n        let response = await cacheWrapper.match({\n            cacheName: this._cacheName,\n            request,\n            event,\n            matchOptions: this._matchOptions,\n            plugins: this._plugins,\n        });\n        let error;\n        if (response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this._cacheName}'` +\n                    ` cache. Will update with the network response in the background.`);\n            }\n            if (event) {\n                try {\n                    event.waitUntil(fetchAndCachePromise);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logger.warn(`Unable to ensure service worker stays alive when ` +\n                            `updating cache for '${getFriendlyURL(request.url)}'.`);\n                    }\n                }\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this._cacheName}' cache. ` +\n                    `Will wait for the network response.`);\n            }\n            try {\n                response = await fetchAndCachePromise;\n            }\n            catch (err) {\n                error = err;\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart('StaleWhileRevalidate', request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n    /**\n     * @param {Object} options\n     * @param {Request} options.request\n     * @param {Event} [options.event]\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    async _getFromNetwork({ request, event }) {\n        const response = await fetchWrapper.fetch({\n            request,\n            event,\n            fetchOptions: this._fetchOptions,\n            plugins: this._plugins,\n        });\n        const cachePutPromise = cacheWrapper.put({\n            cacheName: this._cacheName,\n            request,\n            response: response.clone(),\n            event,\n            plugins: this._plugins,\n        });\n        if (event) {\n            try {\n                event.waitUntil(cachePutPromise);\n            }\n            catch (error) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn(`Unable to ensure service worker stays alive when ` +\n                        `updating cache for '${getFriendlyURL(request.url)}'.`);\n                }\n            }\n        }\n        return response;\n    }\n}\nexport { StaleWhileRevalidate };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { registerQuotaErrorCallback } from 'workbox-core/registerQuotaErrorCallback.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheExpiration } from './CacheExpiration.js';\nimport './_version.js';\n/**\n * This plugin can be used in the Workbox APIs to regularly enforce a\n * limit on the age and / or the number of cached requests.\n *\n * Whenever a cached request is used or updated, this plugin will look\n * at the used Cache and remove any old or extra requests.\n *\n * When using `maxAgeSeconds`, requests may be used *once* after expiring\n * because the expiration clean up will not have occurred until *after* the\n * cached request has been used. If the request has a \"Date\" header, then\n * a light weight expiration check is performed and the request will not be\n * used immediately.\n *\n * When using `maxEntries`, the entry least-recently requested will be removed\n * from the cache first.\n *\n * @memberof module:workbox-expiration\n */\nclass ExpirationPlugin {\n    /**\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {boolean} [config.purgeOnQuotaError] Whether to opt this cache in to\n     * automatic deletion if the available storage quota has been exceeded.\n     */\n    constructor(config = {}) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when a `Response` is about to be returned\n         * from a [Cache](https://developer.mozilla.org/en-US/docs/Web/API/Cache) to\n         * the handler. It allows the `Response` to be inspected for freshness and\n         * prevents it from being used if the `Response`'s `Date` header value is\n         * older than the configured `maxAgeSeconds`.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache the response is in.\n         * @param {Response} options.cachedResponse The `Response` object that's been\n         *     read from a cache and whose freshness should be checked.\n         * @return {Response} Either the `cachedResponse`, if it's\n         *     fresh, or `null` if the `Response` is older than `maxAgeSeconds`.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ event, request, cacheName, cachedResponse }) => {\n            if (!cachedResponse) {\n                return null;\n            }\n            const isFresh = this._isResponseDateFresh(cachedResponse);\n            // Expire entries to ensure that even if the expiration date has\n            // expired, it'll only be used once.\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            dontWaitFor(cacheExpiration.expireEntries());\n            // Update the metadata for the request URL to the current timestamp,\n            // but don't `await` it as we don't want to block the response.\n            const updateTimestampDone = cacheExpiration.updateTimestamp(request.url);\n            if (event) {\n                try {\n                    event.waitUntil(updateTimestampDone);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // The event may not be a fetch event; only log the URL if it is.\n                        if ('request' in event) {\n                            logger.warn(`Unable to ensure service worker stays alive when ` +\n                                `updating cache entry for ` +\n                                `'${getFriendlyURL(event.request.url)}'.`);\n                        }\n                    }\n                }\n            }\n            return isFresh ? cachedResponse : null;\n        };\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when an entry is added to a cache.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache that was updated.\n         * @param {string} options.request The Request for the cached entry.\n         *\n         * @private\n         */\n        this.cacheDidUpdate = async ({ cacheName, request }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                assert.isType(cacheName, 'string', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'cacheName',\n                });\n                assert.isInstance(request, Request, {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'request',\n                });\n            }\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            await cacheExpiration.updateTimestamp(request.url);\n            await cacheExpiration.expireEntries();\n        };\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._config = config;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheExpirations = new Map();\n        if (config.purgeOnQuotaError) {\n            registerQuotaErrorCallback(() => this.deleteCacheAndMetadata());\n        }\n    }\n    /**\n     * A simple helper method to return a CacheExpiration instance for a given\n     * cache name.\n     *\n     * @param {string} cacheName\n     * @return {CacheExpiration}\n     *\n     * @private\n     */\n    _getCacheExpiration(cacheName) {\n        if (cacheName === cacheNames.getRuntimeName()) {\n            throw new WorkboxError('expire-custom-caches-only');\n        }\n        let cacheExpiration = this._cacheExpirations.get(cacheName);\n        if (!cacheExpiration) {\n            cacheExpiration = new CacheExpiration(cacheName, this._config);\n            this._cacheExpirations.set(cacheName, cacheExpiration);\n        }\n        return cacheExpiration;\n    }\n    /**\n     * @param {Response} cachedResponse\n     * @return {boolean}\n     *\n     * @private\n     */\n    _isResponseDateFresh(cachedResponse) {\n        if (!this._maxAgeSeconds) {\n            // We aren't expiring by age, so return true, it's fresh\n            return true;\n        }\n        // Check if the 'date' header will suffice a quick expiration check.\n        // See https://github.com/GoogleChromeLabs/sw-toolbox/issues/164 for\n        // discussion.\n        const dateHeaderTimestamp = this._getDateHeaderTimestamp(cachedResponse);\n        if (dateHeaderTimestamp === null) {\n            // Unable to parse date, so assume it's fresh.\n            return true;\n        }\n        // If we have a valid headerTime, then our response is fresh iff the\n        // headerTime plus maxAgeSeconds is greater than the current time.\n        const now = Date.now();\n        return dateHeaderTimestamp >= now - (this._maxAgeSeconds * 1000);\n    }\n    /**\n     * This method will extract the data header and parse it into a useful\n     * value.\n     *\n     * @param {Response} cachedResponse\n     * @return {number|null}\n     *\n     * @private\n     */\n    _getDateHeaderTimestamp(cachedResponse) {\n        if (!cachedResponse.headers.has('date')) {\n            return null;\n        }\n        const dateHeader = cachedResponse.headers.get('date');\n        const parsedDate = new Date(dateHeader);\n        const headerTime = parsedDate.getTime();\n        // If the Date header was invalid for some reason, parsedDate.getTime()\n        // will return NaN.\n        if (isNaN(headerTime)) {\n            return null;\n        }\n        return headerTime;\n    }\n    /**\n     * This is a helper method that performs two operations:\n     *\n     * - Deletes *all* the underlying Cache instances associated with this plugin\n     * instance, by calling caches.delete() on your behalf.\n     * - Deletes the metadata from IndexedDB used to keep track of expiration\n     * details for each Cache instance.\n     *\n     * When using cache expiration, calling this method is preferable to calling\n     * `caches.delete()` directly, since this will ensure that the IndexedDB\n     * metadata is also cleanly removed and open IndexedDB instances are deleted.\n     *\n     * Note that if you're *not* using cache expiration for a given cache, calling\n     * `caches.delete()` and passing in the cache's name should be sufficient.\n     * There is no Workbox-specific method needed for cleanup in that case.\n     */\n    async deleteCacheAndMetadata() {\n        // Do this one at a time instead of all at once via `Promise.all()` to\n        // reduce the chance of inconsistency if a promise rejects.\n        for (const [cacheName, cacheExpiration] of this._cacheExpirations) {\n            await self.caches.delete(cacheName);\n            await cacheExpiration.delete();\n        }\n        // Reset this._cacheExpirations to its initial state.\n        this._cacheExpirations = new Map();\n    }\n}\nexport { ExpirationPlugin };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport { assert } from './_private/assert.js';\nimport { quotaErrorCallbacks } from './models/quotaErrorCallbacks.js';\nimport './_version.js';\n/**\n * Adds a function to the set of quotaErrorCallbacks that will be executed if\n * there's a quota error.\n *\n * @param {Function} callback\n * @memberof module:workbox-core\n */\nfunction registerQuotaErrorCallback(callback) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(callback, 'function', {\n            moduleName: 'workbox-core',\n            funcName: 'register',\n            paramName: 'callback',\n        });\n    }\n    quotaErrorCallbacks.add(callback);\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Registered a callback to respond to quota errors.', callback);\n    }\n}\nexport { registerQuotaErrorCallback };\n"]}