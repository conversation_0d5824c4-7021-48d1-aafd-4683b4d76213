{"words": {"register": "Register", "ok": "OK", "back": "Back", "proceed": "Proceed / Config", "save_change": "Save Changes", "delete_device": "Delete Device", "active": "Active", "inactive": "Inactive", "license": "License", "expired": "Expired", "period": "Period", "phone_number": "Phone Number", "device_number": "Device Number", "4g_device_number": "4g Device Number", "device_type": "Device Type", "uix_type": "UIX Type", "mobile_number": "Mobile Number", "create_code": "Create Pin Code", "change_code": "Change Pin Code", "nickname": "Your nick name", "old_pin": "Old Code", "new_pin": "New Code", "confirm_pin": "Confirm Code", "delete": "Delete", "edit": "Edit", "location": "Location", "detail": "Detail", "device": "Choose Your Device", "driver_index": "Driver Index", "device_4g": "4GD", "device_sms": "SMS", "get_status": "getting current status and Location of the car....", "default_device": "<PERSON>", "details": "Detail", "update": "Update", "subscribe": "Sub", "unsubscribe": "Unsub", "set_button": "Set Time", "add_btn": "Add", "ble_device": "BLE Device", "ble_index": "BLE Index", "gps_time_select": "Report Time", "license_has_expired": " Your account's license is expired", "get_new_key": "please get a new key", "expired_message": "Please extend license", "rentable_device": "Is Rentable", "device_name": "Device Name", "pay": "Pay with QPay", "start_rent": "Start Rent", "finish_rent": "Finish Rent", "driver_license": "Driver license", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON>", "save": "Save", "not_available": "Not Available"}, "home": {"unlock": "Unlock", "lock": "Lock", "off1": "Off1", "on1": "On1", "off2": "Off2", "on2": "On2", "stop": "Stop", "start": "Start", "device_type": "Device Type", "uix_type": "UIX type", "turn_on": "engine on sent success..", "turn_off": "engine off sent success..", "door_open": "unlock the door sent..", "door_close": "lock the door sent..", "informations": "Informations", "device_not_available": "device is not available", "speed": "speed", "unknown": "unknown", "device_name": "Name", "show_location": "Show Location", "hide_location": "Hide Location", "refresh": "Refresh"}, "message": {}, "configure_drivers": {"numbers_of_drivers": "Numbers of Drivers"}, "menu": {"home": "Home", "user_management": "User management", "device_manage": "device management", "device_dashboard": "online devices", "profile": "Profile", "device": "License", "register": "Device Profile", "nickname": "Nickname & Pin code", "log": "Command Logs", "log_out": "Log out", "license": "License Logs", "mapLog": "Play on Map", "simLog": "Sim card Logs", "driver": "Configure Drivers", "gps": "GPS Logs", "time": "Control with time", "order": "Order", "rent": "Car Rent", "transactions": "Transactions", "withdraws": "Withdraws", "rentcars": "Rent Cars", "help": "sms command list", "device_config": "Device Configuration"}, "pinModal": {"title": "A strong pin code privacy your device", "mismatch_error": "Pin code mismatch "}, "device_profile": {"gps_information": "GPS Config", "device_information": "Device Information", "device_scan": "<PERSON><PERSON>", "license_information": "License Information", "request_license": "Request License", "increase_license": "Increase License", "total_price": "Total Price", "registered_bles": "BLE list"}, "device_log": {"device_number": "Device Number", "command": "Command", "sent": "Sent?", "response": "Response", "time_of_sent": "Time of Sent", "time_of_receive": "Time of Receive", "response_message": "Response Message", "http_or_hook": "Http or Hook"}, "invoice_log": {"invoice_number": "Invoice Number", "license_key": "License Key", "cost": "Cost", "created": "Created", "expired": "Expired", "real_invoice_id": "Real Invoice ID"}, "order": {"order_detail": "Order detail", "car_model": "Car Model", "date_time": "available time for installation", "spare_key": "Spare Key", "yes": "yes", "no": "no", "submit_order": "submit order", "order_price": "order deposit", "address": "address", "order_pricing": "Price: 200.000 with spare key, 230.000 without spare key"}, "driver": {"information": "Driver Information", "name": "Username", "address": "Address", "profile": "profile", "description": "ID of cityzen", "hint": "Та энэхүү цонхыг зөвхөн нэг л удаа бөглөх тул үнэн зөв бөглөн үү", "bank_name": "Bank Name", "bank_account": "Bank Account"}, "landing": {"hero-1-title": "Hero 1 title", "hero-1-label": "Hero 1 label", "hero-1-description": "Hero 1 description"}, "device_config": {"title": "Device Configuration", "device_number": "Device Number", "mqtt_broker": "Pool Broker", "mqtt_connected": "Connected", "mqtt_disconnected": "Disconnected", "mqtt_connection_failed": "Connection Failed", "firmware_version": "Firmware Version", "refresh_status": "Refresh Status", "update_firmware": "Update Firmware", "update_initiated": "Firmware update initiated", "update_failed": "Firmware update failed", "restart_device": "<PERSON><PERSON>", "restart_initiated": "Device restart initiated", "restart_failed": "Device restart failed", "update_sending": "Sending update command...", "update_waiting_response": "Waiting for device response...", "update_downloading": "Downloading firmware...", "update_installing": "Installing firmware...", "update_completed": "Update completed successfully!", "update_already_latest": "<PERSON><PERSON> is already on the latest version", "update_operation_failed": "Update failed - too many attempts, please try again later", "update_timeout": "Update timeout - device did not respond", "update_elapsed": "Elapsed time", "sound_enabled": "Sound Enabled", "sound_help": "Enable/disable device beep sounds", "key_enabled": "Key Detection", "key_help": "Enable/disable key detection functionality", "server": "Server", "server_help": "Select which server to connect to", "protocol": "Protocol", "protocol_help": "Switch between XMPP and LwM2M protocols", "auto_shutdown": "Auto Shutdown Timer (minutes)", "auto_shutdown_help": "Time before device automatically powers off (0-120 minutes)", "auto_shutdown_feature": "Auto Shutdown Feature", "auto_shutdown_feature_help": "Enable/disable auto-shutdown functionality", "log_level": "Log Level", "log_level_help": "Set the verbosity of device logging", "geely_atlas_mode": "<PERSON><PERSON>", "geely_atlas_help": "Special key control mode for Geely Atlas vehicles", "voltage_offset": "Voltage Offset", "voltage_offset_help": "Adjust the reported battery voltage (0-2V)", "notifications_enabled": "Notifications Enabled", "notifications_help": "Enable/disable voltage change notifications", "voltage_threshold": "Voltage Threshold", "voltage_threshold_help": "Set the threshold for voltage change notifications (0-2V)", "gps_enabled": "GPS Enabled", "gps_help": "Enable/disable GPS tracking functionality", "save_success": "Configuration saved successfully", "save_error": "Failed to save configuration"}}