<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Direct Test</title>
    <script src="https://unpkg.com/mqtt/dist/mqtt.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            background-color: #f8f9fa;
            padding: 16px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
        .log-entry {
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.success {
            color: #28a745;
        }
        .broker-card {
            margin-bottom: 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
        }
        .broker-status {
            font-weight: bold;
            margin-top: 8px;
        }
        .broker-status.connected {
            color: #28a745;
        }
        .broker-status.disconnected {
            color: #dc3545;
        }
        .broker-status.connecting {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MQTT Direct Test</h1>
        
        <div class="card">
            <h2>Test All Brokers</h2>
            <p>Click the button below to test connections to all MQTT brokers:</p>
            <button id="testAll">Test All Brokers</button>
            <button id="clearLog">Clear Log</button>
        </div>
        
        <div id="brokers-container">
            <!-- Broker cards will be added here -->
        </div>
        
        <div class="card">
            <h2>Log</h2>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Broker configurations to test
        const brokers = [
            {
                id: 1,
                name: "Primary Broker",
                host: "*************",
                port: 8083,
                path: "/mqtt",
                protocol: "ws"
            },
            {
                id: 2,
                name: "Secondary Broker",
                host: "************",
                port: 8083,
                path: "/mqtt",
                protocol: "ws"
            },
            {
                id: 3,
                name: "Tertiary Broker",
                host: "**************",
                port: 8083,
                path: "/mqtt",
                protocol: "ws"
            },
            {
                id: 4,
                name: "Public EMQX Broker",
                host: "broker.emqx.io",
                port: 8083,
                path: "/mqtt",
                protocol: "ws"
            }
        ];
        
        // Create broker cards
        const brokersContainer = document.getElementById('brokers-container');
        brokers.forEach(broker => {
            const card = document.createElement('div');
            card.className = 'broker-card';
            card.innerHTML = `
                <h3>${broker.name}</h3>
                <p>URL: ${broker.protocol}://${broker.host}:${broker.port}${broker.path}</p>
                <div class="broker-status disconnected" id="broker-status-${broker.id}">Disconnected</div>
                <button id="test-broker-${broker.id}">Test Connection</button>
                <button id="publish-broker-${broker.id}" disabled>Publish Test Message</button>
            `;
            brokersContainer.appendChild(card);
            
            // Add event listener for test button
            document.getElementById(`test-broker-${broker.id}`).addEventListener('click', () => {
                testBroker(broker);
            });
            
            // Add event listener for publish button
            document.getElementById(`publish-broker-${broker.id}`).addEventListener('click', () => {
                publishTestMessage(broker);
            });
        });
        
        // Log function
        const logElement = document.getElementById('log');
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Clear log button
        document.getElementById('clearLog').addEventListener('click', () => {
            logElement.innerHTML = '';
        });
        
        // Test all brokers button
        document.getElementById('testAll').addEventListener('click', () => {
            log('Testing all brokers...');
            brokers.forEach((broker, index) => {
                setTimeout(() => {
                    testBroker(broker);
                }, index * 1000); // Stagger connections by 1 second
            });
        });
        
        // Store client connections
        const clients = {};
        
        // Test a single broker
        function testBroker(broker) {
            // Update status
            const statusElement = document.getElementById(`broker-status-${broker.id}`);
            statusElement.className = 'broker-status connecting';
            statusElement.textContent = 'Connecting...';
            
            // Disable publish button
            document.getElementById(`publish-broker-${broker.id}`).disabled = true;
            
            // Log connection attempt
            log(`Connecting to ${broker.name} (${broker.protocol}://${broker.host}:${broker.port}${broker.path})...`);
            
            try {
                // Create client ID
                const clientId = `direct_test_${broker.id}_${Math.random().toString(16).substring(2, 10)}`;
                
                // Create connection URL
                const url = `${broker.protocol}://${broker.host}:${broker.port}${broker.path}`;
                
                // Connection options
                const options = {
                    clientId: clientId,
                    username: "admin",
                    password: "public",
                    clean: true,
                    keepalive: 60,
                    reconnectPeriod: 1000,
                    connectTimeout: 30000
                };
                
                // Connect to broker
                const client = mqtt.connect(url, options);
                
                // Store client
                clients[broker.id] = client;
                
                // Set up event handlers
                client.on('connect', () => {
                    log(`Connected to ${broker.name} successfully!`, 'success');
                    statusElement.className = 'broker-status connected';
                    statusElement.textContent = 'Connected';
                    
                    // Enable publish button
                    document.getElementById(`publish-broker-${broker.id}`).disabled = false;
                    
                    // Subscribe to test topic
                    client.subscribe('aslaa/test', (err) => {
                        if (err) {
                            log(`Error subscribing to topic on ${broker.name}: ${err.message}`, 'error');
                        } else {
                            log(`Subscribed to aslaa/test on ${broker.name}`, 'success');
                        }
                    });
                });
                
                client.on('error', (err) => {
                    log(`Error with ${broker.name}: ${err.message}`, 'error');
                    statusElement.className = 'broker-status disconnected';
                    statusElement.textContent = `Error: ${err.message}`;
                });
                
                client.on('offline', () => {
                    log(`${broker.name} is offline`, 'error');
                    statusElement.className = 'broker-status disconnected';
                    statusElement.textContent = 'Offline';
                });
                
                client.on('reconnect', () => {
                    log(`Reconnecting to ${broker.name}...`);
                    statusElement.className = 'broker-status connecting';
                    statusElement.textContent = 'Reconnecting...';
                });
                
                client.on('message', (topic, message) => {
                    const msg = message.toString();
                    log(`Received message on ${topic} from ${broker.name}: ${msg}`, 'success');
                    
                    // Try to parse as JSON
                    try {
                        const jsonMsg = JSON.parse(msg);
                        log(`Parsed JSON from ${broker.name}: ${JSON.stringify(jsonMsg, null, 2)}`, 'success');
                    } catch (e) {
                        // Not JSON, that's fine
                    }
                });
            } catch (error) {
                log(`Error creating client for ${broker.name}: ${error.message}`, 'error');
                statusElement.className = 'broker-status disconnected';
                statusElement.textContent = `Error: ${error.message}`;
            }
        }
        
        // Publish test message
        function publishTestMessage(broker) {
            const client = clients[broker.id];
            if (!client) {
                log(`No client available for ${broker.name}`, 'error');
                return;
            }
            
            const message = JSON.stringify({
                test: `Hello from direct test for ${broker.name}`,
                timestamp: new Date().toISOString(),
                broker: broker.host
            });
            
            log(`Publishing to aslaa/test on ${broker.name}: ${message}`);
            client.publish('aslaa/test', message);
        }
    </script>
</body>
</html>
