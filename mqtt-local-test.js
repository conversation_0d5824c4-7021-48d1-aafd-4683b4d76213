const mqtt = require('mqtt');

// MQTT broker configuration for local TCP connection
const options = {
  clientId: `aslaacv_local_${Math.random().toString(16).substring(2, 10)}`,
  clean: true,
  connectTimeout: 4000,
  reconnectPeriod: 1000,
  // TCP specific options
  port: 1883,
  protocol: 'mqtt',
};

// Local MQTT broker URL (change this if your broker is on a different IP)
const brokerUrl = 'mqtt://localhost';

console.log('Connecting to local MQTT broker:', brokerUrl);
console.log('With options:', JSON.stringify(options, null, 2));

// Create MQTT client
const client = mqtt.connect(brokerUrl, options);

// Set up event handlers
client.on('connect', () => {
  console.log('Connected to local MQTT broker successfully!');
  
  // Subscribe to a test topic
  client.subscribe('aslaa/test', (err) => {
    if (!err) {
      console.log('Subscribed to aslaa/test topic');
      
      // Publish a test message
      client.publish('aslaa/test', 'Hello from local TCP test', { qos: 0 }, (error) => {
        if (!error) {
          console.log('Published test message to aslaa/test');
        } else {
          console.error('Error publishing:', error.message);
        }
      });
    } else {
      console.error('Error subscribing:', err.message);
    }
  });
  
  // Set a timeout to disconnect after 10 seconds
  setTimeout(() => {
    console.log('Test completed, disconnecting...');
    client.end();
    process.exit(0);
  }, 10000);
});

client.on('message', (topic, message) => {
  const msg = message.toString();
  console.log(`Received message on ${topic}: ${msg}`);
});

client.on('error', (err) => {
  console.error('MQTT Error:', err.message);
  console.error('Error details:', JSON.stringify(err, Object.getOwnPropertyNames(err)));
});

client.on('close', () => {
  console.log('Connection closed');
});

client.on('offline', () => {
  console.log('Client is offline');
});

client.on('reconnect', () => {
  console.log('Attempting to reconnect...');
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Disconnecting...');
  client.end();
  process.exit(0);
});
